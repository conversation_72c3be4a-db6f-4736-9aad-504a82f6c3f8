# useCustomDropzone Hook

A reusable dropzone hook that provides a flexible and customizable file upload interface using `react-dropzone`.

## ✨ Key Features

- **Automatic Error Handling**: Errors are handled automatically with toast notifications - no need to provide `onFilesRejected` callback
- **Flexible file type acceptance**: Predefined file types or custom configurations
- **Configurable file size limits**: Set maximum file size per upload
- **Multiple file support**: Control single or multiple file uploads
- **Custom error handling**: Customizable error messages with toast notifications
- **Built-in validation**: Automatic file type and size validation
- **TypeScript support**: Full type safety and IntelliSense
- **Debug logging**: Automatic console warnings for rejected files

## 🚀 Automatic Error Handling

**The hook automatically handles all errors even when `onFilesRejected` is not provided!**

```typescript
// ✅ This works perfectly - errors are handled automatically
const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: FILE_TYPES.IMAGES,
  maxFiles: 1,
  maxSize: 1 * 1024 * 1024,
  onFilesAccepted: (files) => {
    console.log("Files accepted:", files);
  },
  // No onFilesRejected needed - errors show as toast notifications automatically
});
```

## 📖 Basic Usage

```typescript
import { useCustomDropzone, FILE_TYPES } from "../hook/useCustomDropzone";

function MyComponent() {
  const { getRootProps, getInputProps, acceptedFiles } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.EXCEL,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    onFilesAccepted: (files) => {
      console.log("Files accepted:", files);
    },
    // onFilesRejected is optional - errors handled automatically
  });

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      <p>Drag 'n' drop files here, or click to select files</p>
    </div>
  );
}
```

## 🔧 Advanced Usage

### Custom File Types

```typescript
const customFileTypes = {
  "image/jpeg": [".jpg", ".jpeg"],
  "image/png": [".png"],
  "application/pdf": [".pdf"],
};

const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: customFileTypes,
  maxFiles: 5,
  multiple: true,
  maxSize: 5 * 1024 * 1024, // 5MB
  onFilesAccepted: (files) => {
    console.log("Files accepted:", files);
  },
  // Errors still handled automatically
});
```

### Custom Error Messages

```typescript
const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: FILE_TYPES.IMAGES,
  customErrorMessages: {
    "file-invalid-type": "Please upload only image files (JPG, PNG, GIF, WebP)",
    "file-too-large": "Image must be smaller than 2MB",
    "too-many-files": "You can only upload one image at a time",
  },
  onFilesAccepted: (files) => {
    console.log("Files accepted:", files);
  },
  // Custom error messages will be shown automatically
});
```

### When to Use onFilesRejected

You only need `onFilesRejected` if you want to perform additional custom actions beyond the automatic error handling:

```typescript
const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: FILE_TYPES.DOCUMENTS,
  onFilesAccepted: (files) => {
    console.log("Files accepted:", files);
  },
  onFilesRejected: (rejections) => {
    // Custom logic in ADDITION to automatic toast notifications
    console.log("Custom rejection handling:", rejections);

    // Maybe update some state, send analytics, etc.
    setRejectedFilesList(rejections.map((r) => r.file.name));
  },
});
```

### Silent Mode (No Automatic Toasts)

```typescript
const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: FILE_TYPES.ALL_FILES,
  showToastOnError: false, // Disable automatic toasts
  onFilesAccepted: (files) => {
    console.log("Files accepted:", files);
  },
  onFilesRejected: (rejections) => {
    // MUST handle errors manually when showToastOnError is false
    console.error("Files rejected:", rejections);
  },
});
```

## 🛡️ Error Handling Details

The hook provides three levels of error handling:

1. **Automatic Toast Notifications** (default): Errors are shown as toast messages
2. **Console Warnings**: All rejections are logged for debugging
3. **Custom Callbacks**: Optional `onFilesRejected` for additional handling

### Error Types Handled

- **file-invalid-type**: Wrong file type uploaded
- **file-too-large**: File exceeds maximum size limit
- **too-many-files**: More files selected than allowed
- **default**: Any other error

### Error Handling Flow

```
File Upload Attempt
        ↓
   Validation Check
        ↓
    ✅ Success → onFilesAccepted callback
    ❌ Failure → Automatic toast + console warning + optional onFilesRejected
```

## 🎯 Migration Guide

### From Original useDropzone

**Before:**

```typescript
const { getRootProps, getInputProps } = useDropzone({
  accept: { "image/*": [] },
  maxFiles: 1,
  maxSize: 1024 * 1024,
  onDrop: (acceptedFiles) => {
    setFiles(acceptedFiles);
  },
  onDropRejected: (rejections) => {
    console.log("Error:", rejections);
    toast.error("Upload failed");
  },
});
```

**After:**

```typescript
const { getRootProps, getInputProps } = useCustomDropzone({
  acceptedFileTypes: FILE_TYPES.IMAGES,
  maxFiles: 1,
  maxSize: 1024 * 1024,
  onFilesAccepted: (acceptedFiles) => {
    setFiles(acceptedFiles);
  },
  // onFilesRejected removed - errors handled automatically!
});
```

## 📋 API Reference

### UseCustomDropzoneProps

| Prop                  | Type                                    | Default            | Description                          |
| --------------------- | --------------------------------------- | ------------------ | ------------------------------------ |
| `acceptedFileTypes`   | `Record<string, string[]>`              | `FILE_TYPES.EXCEL` | File types to accept                 |
| `maxFiles`            | `number`                                | `1`                | Maximum number of files              |
| `maxSize`             | `number`                                | `1024*1024` (1MB)  | Maximum file size in bytes           |
| `multiple`            | `boolean`                               | `false`            | Allow multiple files                 |
| `onFilesAccepted`     | `(files: FileWithPath[]) => void`       | `undefined`        | Callback when files are accepted     |
| `onFilesRejected`     | `(rejections: FileRejection[]) => void` | `undefined`        | **Optional** - Custom error handling |
| `showToastOnError`    | `boolean`                               | `true`             | Show automatic toast notifications   |
| `customErrorMessages` | `Partial<ErrorMessages>`                | `{}`               | Custom error messages                |
| `additionalOptions`   | `Partial<DropzoneOptions>`              | `{}`               | Additional react-dropzone options    |

### FILE_TYPES Constants

- `FILE_TYPES.EXCEL` - Excel files (.xlsx, .xls, .csv)
- `FILE_TYPES.IMAGES` - Image files (.jpg, .jpeg, .png, .gif, .webp)
- `FILE_TYPES.DOCUMENTS` - Document files (.pdf, .doc, .docx)
- `FILE_TYPES.ALL_FILES` - All file types

### Utility Functions

- `formatFileSize(bytes: number)` - Format file size in human-readable format
- `getFileExtension(filename: string)` - Get file extension from filename

## ⚡ Performance Notes

- Automatic error handling adds minimal overhead
- Console warnings can be disabled in production if needed
- File previews are created efficiently with URL.createObjectURL
- Memory cleanup is handled automatically

## 🔍 Debugging

All file rejections are automatically logged to the console with detailed information:

```javascript
console.warn("File upload rejected:", [
  {
    fileName: "document.pdf",
    errors: [{ code: "file-invalid-type", message: "File type not accepted" }],
  },
]);
```
