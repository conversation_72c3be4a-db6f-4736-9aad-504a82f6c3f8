import { useState } from "react";
import { useNavigate } from "react-router";
import { ChevronLeft } from "lucide-react";

import { cn } from "@/lib/utils";
import { COLOR_LIST } from "@/constant/data";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import BussinessInfo from "@/components/common/bussiness-info";
import { useAuth } from "@/context/AuthContext";
import { useCreateCalendar } from "@/api/calendar";
import { generateUUID } from "@/utils";
import { toast } from "sonner";

export default function AddCalendarScreen() {
  const navigate = useNavigate();

  const { userId } = useAuth();

  const { mutate: createCalendar, isPending: isCreateCalendarPending } =
    useCreateCalendar();

  const [colorCode, setColor] = useState(COLOR_LIST[0]);
  const [calendarName, setCalendarName] = useState("");

  const [errors, setErrors] = useState({ calendarName: false });

  const handleCreateCalendar = () => {
    if (!calendarName) {
      setErrors({ calendarName: true });
      return;
    }

    if (userId) {
      createCalendar(
        {
          calendarName,
          color: colorCode,
          deleted: false,
          userType: "endUser",
          usrId: userId,
          _id: generateUUID("cal"),
        },
        {
          onSuccess() {
            toast.success("Calendar created successfully");
            navigate(`/calendar`);
          },
          onError(error) {
            toast.error(error.message);
          },
        }
      );
    }
  };

  return (
    <div className="flex flex-col w-full h-dvh ">
      <div className="flex justify-between">
        {/* screen name */}
        <div className="flex w-full px-6">
          <p className="text-base lg:text-xl mt-auto">Create a Calendar</p>
        </div>
        <div className="w-52 lg:w-72 ml-auto pt-2">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex flex-col gap-2 w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] pb-3 pr-3 mt-2">
        <div className="bg-white w-full h-full overflow-hidden flex flex-col p-4 lg:p-6 gap-4 rounded-2xl overflow-y-auto no-scrollbar">
          {/* calendar details */}
          <div>
            <Button
              variant={"ghost"}
              className="h-6 lg:h-8 text-sm lg:text-base text-fb-warn-600"
              onClick={() => navigate(-1)}>
              <ChevronLeft className=" lg:!size-6 text-black" />
              Back to calendars
            </Button>
          </div>
          <div>
            <p className="font-semibold text-xl lg:text-2xl pl-3">
              New Calendar
            </p>
          </div>
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 w-full">
            <div className="flex gap-2 flex-col justify-between w-full">
              <div className="flex flex-col gap-0.5 w-full">
                <Label
                  htmlFor="calendar-name"
                  className="text-black text-base font-normal px-4 flex gap-0.5 text-center">
                  Calendar Name*
                </Label>
                <Input
                  id="calendar-name"
                  placeholder="Calendar name"
                  value={calendarName}
                  onChange={(e) => {
                    setCalendarName(e.target.value);
                    setErrors((old) => ({ ...old, calendarName: false }));
                  }}
                  className={cn(errors.calendarName && "border-fb-warn-500")}
                />
                {errors.calendarName && (
                  <p className="text-xs text-fb-warn-500 font-medium px-4">
                    Enter calendar name
                  </p>
                )}
              </div>
            </div>
            <div className="flex flex-col w-full">
              <p className="text-base text-black px-4">
                Choose a Colour for you Calendar*
              </p>
              <div className="flex gap-2 flex-wrap ">
                {COLOR_LIST.map((color, id) => {
                  return (
                    <div
                      key={id}
                      onClick={() => setColor(color)}
                      style={{
                        backgroundColor: color,
                        borderWidth: colorCode === color ? 2 : 0,
                        borderColor: "#000",
                      }}
                      className="w-10 h-10 md:w-16 md:h-16 rounded-12px cursor-pointer"
                    />
                  );
                })}
              </div>
            </div>
          </div>

          <div className="flex justify-center mt-2">
            <Button
              disabled={isCreateCalendarPending}
              className="bg-fb-bPrime-600 rounded-full h-9 w-64 "
              onClick={handleCreateCalendar}>
              {isCreateCalendarPending ? "Creating..." : "Create Calendar"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
