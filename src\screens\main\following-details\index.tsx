import { useState } from "react";
import { ChevronLeft, Users } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { But<PERSON> } from "@/components/ui/button";
import YoutubeImg from "@/assets/images/youtube.png";
import WwwLinkImg from "@/assets/images/www-link.png";
import PintressImg from "@/assets/images/pintress.png";
import InstagramImg from "@/assets/images/instagram.png";
import YearCalendar from "@/components/calendar/year-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import EventsDateWise from "@/components/common/event/date-wise-view";
import { CalendarModelT, EventModelT, BizzEventModelT } from "@/types";
import MonthCalendarDateView from "@/components/calendar/month-calendar-date-view";
import CalendarTabSwitcher, {
  AvailableCalendarTabs,
} from "@/components/calendar/calendar-tab-switch";
import SideCalendarView from "@/components/common/side-calendar-view";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import WeeklyCalendar from "@/components/calendar/weekly-calendar";
import MonthCalendar from "@/components/calendar/month-calendar";
import { useAuth } from "@/context/AuthContext";

export default function FollowingDetailsScreen() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { userId } = useAuth();

  const calendarData: CalendarModelT[] = [];

  const { events, TodoList } = useCalendarsWithEvents(userId);

  const [data] = useState<(EventModelT | BizzEventModelT)[]>([]);
  const [activeTab, setActiveTab] = useState<AvailableCalendarTabs>("Week");

  const DummyCalendarDetails = {
    calendarName: "Calendar Family",
    calendarId: "@family",
    image: "https://picsum.photos/600",
    follower: 15,
    description: "About the Calenda About the Calenda",
  };

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
  };

  return (
    <div className="flex flex-col w-full h-dvh">
      <div className="justify-between flex items-end">
        <button
          className="pl-4 flex gap-1 items-center"
          onClick={() => navigate(-1)}>
          <ChevronLeft className="size-4" />
          <p className="text-sm lg:text-sm">Back </p>
        </button>
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex w-full md:h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 pr-2 overflow-y-auto">
        {/* side bar */}
        <div className="w-64 lg:w-80 min-w-64 lg:min-w-80 flex flex-col pr-3 pt-3 gap-2 overflow-y-auto no-scrollbar">
          <div className="bg-fb-bPrime-50 p-2 rounded-xl drop-shadow-cardDeatilsShadow flex flex-col gap-1">
            <div className="h-48 w-full rounded-xl overflow-hidden relative">
              <img
                src={DummyCalendarDetails.image}
                alt=""
                className="w-full h-full object-cover"
              />
              <div className="bg-black/50 absolute top-0 left-0 w-full h-full" />
            </div>
            <div className="flex flex-col ">
              <p className="font-semibold text-base lg:text-xl !leading-5 -tracking-wide">
                {DummyCalendarDetails.calendarName}
              </p>
              <p className="text-xs lg:text-sm font-light italic">
                {DummyCalendarDetails.calendarId}
              </p>
            </div>
            {id === "calendar" ? (
              <div className="flex gap-2 text-xs">
                <Users className="size-3" /> 15 Followers
              </div>
            ) : (
              <div className="flex gap-2">
                <div className="w-5 h-5">
                  <img
                    src={PintressImg}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="w-5 h-5">
                  <img
                    src={WwwLinkImg}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="w-5 h-5">
                  <img
                    src={InstagramImg}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="w-5 h-5">
                  <img
                    src={YoutubeImg}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            )}
            <div className="flex flex-col">
              <p className="font-semibold text-xs md:text-sm leading-4 tracking-wide text-black/75">
                About Event
              </p>
              <p className="line-clamp-2 overflow-ellipsis text-xxxs md:text-xxs text-black/75">
                {DummyCalendarDetails.description}
              </p>
            </div>
          </div>
          {activeTab != "Upcoming" && (
            <div className="flex w-full h-full  justify-center items-center">
              <SideCalendarView />
            </div>
          )}
          {/* calendar view */}
          {activeTab === "Upcoming" && <SideCalendarView />}
        </div>
        {/* calender views */}
        {id === "calendar" ? (
          <div className="flex flex-col gap-2 py-3 w-[calc(100%-250px)] h-full bg-white rounded-2xl">
            <CalendarTabSwitcher
              defaultTab={activeTab}
              onTabChange={handleTabChange}
            />

            {activeTab === "Upcoming" && (
              <div className="bg-white w-full h-full flex flex-col p-6 rounded-2xl overflow-y-auto no-scrollbar">
                <EventsDateWise events={data} />
              </div>
            )}
            {activeTab === "Week" && (
              <WeeklyCalendar events={events} Todos={TodoList} />
            )}
            {activeTab === "Month" && <MonthCalendar events={events} />}
            {activeTab === "Year" && <YearCalendar />}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-4 p-3 lg:p-6 w-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
            {calendarData.map((row, ind) => {
              return <CalendarView data={row} key={ind} />;
            })}
          </div>
        )}
      </div>
    </div>
  );
}

const CalendarView = ({ data }: { data: CalendarModelT }) => {
  const { _id, calendarName } = data;

  const isUserFollow = false;

  const cuurentCalendar = {
    month: new Date().getMonth(),
    year: new Date().getFullYear(),
  };

  return (
    <div className="p-4 flex gap-2 rounded-xl bg-white  shadow-calenViewShadow h-fit items-center">
      <div className="flex flex-col gap-1 xl:gap-3 h-full justify-center items-center">
        <div className="rounded-full w-20 h-20 xl:w-32 xl:h-32 overflow-hidden border-fb-neutral-300 border bg-fb-cadelBlue-500">
          <img src={""} alt="" className="w-full h-full object-cover" />
        </div>
        <div className="text-center w-20 xl:w-32">
          <p className="md:text-sm xl:text-xl leading-4 font-semibold truncate">
            {calendarName}
          </p>
          <p className="md:text-xs xl:text-base truncate">{_id}</p>
        </div>
        {isUserFollow ? (
          <Button className="bg-white drop-shadow-buttonShadow text-black !h-7 lg:h-8 w-full hover:bg-fb-neutral-100">
            Following
          </Button>
        ) : (
          <Button className="bg-fb-bPrime-600 drop-shadow-buttonShadow text-white !h-7 lg:h-8 w-full">
            Follow
          </Button>
        )}
      </div>
      <MonthCalendarDateView
        month={cuurentCalendar.month}
        year={cuurentCalendar.year}
        isSmallView
        className="text-xxs lg:!text-xs w-full"
      />
    </div>
  );
};
