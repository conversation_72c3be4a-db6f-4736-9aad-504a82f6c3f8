import { Query_Key } from "@/constant/data";
import {
  NotificatinMarkAsReadFormT,
  NotificationResponseT,
  StatusCodes,
} from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";

export const useGetAllNotifications = (payload: { usrId?: string | null }) => {
  return useQuery<NotificationResponseT>({
    queryKey: [Query_Key.allNotifications],
    queryFn: () =>
      Api_Get_Notifications({ recipientUsrId: payload.usrId || null }),
    refetchInterval: 10000, // Auto refresh every 10 seconds
    staleTime: 60000, // Keeps data fresh for 60 seconds before refetching
    enabled: !!payload.usrId, // ⛔ won't run unless usrId is truthy
  });
};

export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Mark_Notification_As_Read,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allNotifications] });
    },
  });
};

const Api_Get_Notifications = async (payload: {
  recipientUsrId: string | null;
}): Promise<NotificationResponseT> => {
  const FailureMessage = "Failed to get notifications...";

  try {
    const response = await API.post(API_ENDPOINTS.notification.getAll, payload);
    const result = handleApiResponse<NotificationResponseT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Mark_Notification_As_Read = async (
  data: NotificatinMarkAsReadFormT
): Promise<null> => {
  const FailureMessage = "Failed to mark notification as read...";

  try {
    const response = await API.post(
      API_ENDPOINTS.notification.markAsRead,
      data
    );
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
