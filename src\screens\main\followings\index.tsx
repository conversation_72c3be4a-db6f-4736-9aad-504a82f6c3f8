import { useState, useMemo } from "react";
import { Link } from "react-router";

import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import BgImage from "@/assets/images/following-bg.png";
import NoLists from "@/assets/images/no-list-view.png";
import { BizzCalendarModelT } from "@/types";
import BussinessInfo from "@/components/common/bussiness-info";
import {
  // useFollowBizzCalendar,
  useGetAllBizzCalendar,
} from "@/api/biz-calendar";
import { useAuth } from "@/context/AuthContext";
import { useGetAccounDetails } from "@/api/account";

export default function FollowingScreen() {
  const [search, setSearch] = useState<string>("");

  const { userId } = useAuth();
  const { data: userAccountData = null } = useGetAccounDetails({
    userId: userId,
  });

  const calenderFollowing = userAccountData?.userData?.following || [];

  const { data: bizzCalendarData = [] } = useGetAllBizzCalendar();

  const calenderFollowList = bizzCalendarData.filter((cal) =>
    calenderFollowing.includes(cal._id)
  );

  const displayList = useMemo(() => {
    if (!bizzCalendarData || bizzCalendarData.length === 0) {
      return [];
    }

    const businessData = bizzCalendarData.filter(d => d.bizId);
    const filteredBizzCalendarData = bizzCalendarData.filter(
      (cal) =>
        cal.businessName?.toLowerCase().includes(search.toLowerCase()) ||
        cal.businessId?.toLowerCase().includes(search.toLowerCase()) ||
        cal.calendarName?.toLowerCase().includes(search.toLowerCase()) ||
        cal.calendarId?.toLowerCase().includes(search.toLowerCase())
    );
    return search === "" ? businessData : filteredBizzCalendarData;
  }, [search, bizzCalendarData]);

  return (
    <div className="flex w-full h-dvh">
      <div className=" pb-3 pt-10 w-[calc(100%-250px)]">
        <div className="flex flex-col   w-full h-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
          {/* search */}
          <div className="flex w-full p-6 sticky top-0 z-20">
            <Input
              id="search"
              name="search"
              value={search}
              placeholder="Search"
              onChange={(e) => setSearch(e.target.value)}
              className="bg-fb-neutral-40 border-none placeholder:text-black/70 h-8 lg:h-9"
            />
          </div>
          {!search && (
            <div className="w-full !inset-0 drop-shadow-buttonShadow ">
              <img
                className="w-full  object-cover object-center h-32 bg-fb-cadelBlue-500"
                src={BgImage}
                alt=""
              />
            </div>
          )}
          {/* no events */}
          <div className="flex flex-col gap-2 w-full p-3 lg:p-6 ">
            <div className="border-b-2 border-b-fb-bPrime-hgts pb-1">
              <p className="font-semibold text-base leading-4">
                You might like these Calendars/Organises
              </p>
            </div>

            <div className="grid grid-cols-3 w-full h-full gap-2 md:gap-3 ">
              {displayList.map((follow, ind) => {
                return (
                  <FollowingList
                    data={follow}
                    key={ind}
                    // isFollowing={calenderFollowing.includes(follow._id)}
                    // userId={userId}
                    isSearching={search !== ""}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {/* events side data */}
      <div className="w-64 lg:w-80  px-4 py-3 flex flex-col gap-3  overflow-y-auto no-scrollbar">
        {/* bussiness details */}

        <BussinessInfo />

        <p className="text-base lg:text-xl font-semibold -tracking-wide">
          Calendars you follow
        </p>
        {/* events */}
        {calenderFollowList.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
            {calenderFollowList.map((cal, ind) => {
              return <CalenderList data={cal} key={ind} />;
            })}
          </div>
        ) : (
          <div className="flex flex-col gap-5 px-3 lg:px-6 w-full h-full justify-center items-center">
            <img src={NoLists} alt="" />
            <p className="font-semibold text-base lg:text-lg text-center">
              You do not follow any calendars
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

const FollowingList = ({
  data,
  // isFollowing,
  // userId,
  isSearching = false
}: {
  data: BizzCalendarModelT;
  // isFollowing: boolean;
  // userId: string | null;
  isSearching?: boolean;
}) => {
  // const { mutate: followBizzCalendar, isPending: isFollowPending } =
  //   useFollowBizzCalendar();
  // const { mutate: unfollowBizzCalendar, isPending: isUnfollowPending } =
  //   useUnfollowBizzCalendar();

  // const handleFollow = () => {
  //   if (userId) {
  //     followBizzCalendar(
  //       { usrId: userId, calId: data._id },
  //       {
  //         onError(error) {
  //           toast.error(error.message);
  //         },
  //       }
  //     );
  //   }
  // };

  // const handleUnfollow = () => {
  //   if (userId) {
  //     unfollowBizzCalendar(
  //       { usrId: userId, calId: data._id },
  //       {
  //         onError(error) {
  //           toast.error(error.message);
  //         },
  //       }
  //     );
  //   }
  // };

  return (
    <div
      className={cn(
        "w-full rounded-xl shadow-cardInnerShadow",
        "bg-white text-black"
      )}>
      <div className="w-full h-44 rounded-xl overflow-hidden">
        <img
          className="w-full h-full object-cover bg-fb-cadelBlue-500"
          src={data.picture || ""}
          alt=""
        />
      </div>
      <div className="py-2 px-4 flex flex-col">
        <div className="flex flex-col">
          <p className="text-xs lg:text-sm -tracking-wide leading-3 font-semibold">
            {data.businessName ?? data.calendarName}
          </p>
          <p className="text-xxs lg:text-xs italic">{data.businessId || data.calendarId || ""}</p>
        </div>

        <Link to={`/${data.businessName ? 'following' : 'calendar'}/${data._id}`}>
          <Button className="text-white text-xs h-6 w-full bg-fb-bPrime-500 hover:bg-fb-bPrime-500/80 mt-3">
            Select {isSearching ? (data.businessName ? "Business" : "Calendar") : ""}
          </Button>
        </Link>
      </div>
    </div>
  );
};

const CalenderList = ({ data }: { data: BizzCalendarModelT }) => {
  return (
    <Link to={`/calendar/${data._id}`}>
      <div className="bg-white p-3 rounded-md gap-2 w-full text-center justify-center items-center flex flex-col drop-shadow-calenViewShadow shadow-calenViewShadow">
        <p className="font-semibold text-base leading-4 truncate">
          {data?.businessName}
        </p>
        <img
          className="bg-fb-cadelBlue-500 w-14 h-14 lg:w-20 lg:h-20 rounded-full"
          src={data.picture || ""}
          alt=""
        />
        <p className="italic text-xs lg:text-base truncate">
          {data?.businessId}
        </p>
      </div>
    </Link>
  );
};
