import { useState } from "react";
import { Link, Navigate, useNavigate } from "react-router";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import GmailPng from "@/assets/images/Gmail.png";
import ApplePng from "@/assets/images/apple.png";
import BgImage from "@/assets/images/bg-image.png";
import FacebookPng from "@/assets/images/facebook.png";
import UserLogo from "@/assets/images/fragment-user-logo.png";
import { useAuth } from "@/context/AuthContext";

export default function SignUpScreen() {
  const { userId } = useAuth();
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");

  const navigate = useNavigate();

  const handleSubmit = () => {
    navigate("/signup-details");
  };

  if (userId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-white flex w-full h-dvh sm:h-screen relative overflow-hidden">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />

      <div className="flex h-full px-4 py-6 w-1/2 z-10 ">
        <div className="bg-fb-neutral-100 flex w-full rounded-3xl px-6 sm:px-12 md:px-16 lg:px-24 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <div className="flex flex-col gap-12 w-full justify-center">
            <div className="border-b pb-1 border-black">
              <p className="text-3xl text-black">Sign Up using your Email Id</p>
            </div>
            <div className="flex flex-col gap-3 w-full   h-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="email"
                  className="text-black font-semibold text-2xl">
                  Email Id*
                </Label>
                <Input
                  id="email"
                  value={email || ""}
                  type="email"
                  placeholder="<EMAIL>"
                  onChange={(e) => setEmail(e.target.value)}
                  className=""
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="password"
                  className="text-black font-semibold text-2xl">
                  Password*
                </Label>
                <Input
                  id="password"
                  value={password || ""}
                  type="password"
                  placeholder="********"
                  onChange={(e) => setPassword(e.target.value)}
                  className=""
                />
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button
                  className="text-xl font-semibold rounded-full text-black bg-fb-uPrime-500 hover:bg-fb-uPrime-500/80"
                  onClick={handleSubmit}>
                  Sign Up
                </Button>
                <div className="flex items-center justify-between px-3">
                  <p className="text-black font-light ">
                    Already have an account?{" "}
                    <Link to={"/login"} className="">
                      Log In
                    </Link>
                  </p>
                </div>
              </div>
              <div className="flex justify-center items-center gap-2 opacity-50">
                <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
                or <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
              </div>
              <div className="flex flex-col gap-2 w-full items-center">
                <p className="text-black">Sign up with</p>
                <div className="flex items-center justify-between gap-5 w-full">
                  <Button className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full">
                    <img src={GmailPng} alt="gmail" className="p-1.5 h-10" />
                    Gmail
                  </Button>
                  <Button className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-900 flex text-white hover:bg-neutral-800 rounded-full">
                    <img src={ApplePng} alt="apple" className="p-1.5 h-10" />
                    Apple Id
                  </Button>
                  <Button className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full">
                    <img
                      src={FacebookPng}
                      alt="facebook"
                      className="p-1.5 h-10"
                    />
                    Facebook
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-center items-center w-1/2 z-10">
        <div className="flex flex-col">
          <div className="mx-auto h-48">
            <img src={UserLogo} alt="" className="h-full object-cover" />
          </div>
          <p className="text-3xl font-bold text-black">your one step</p>
          <p className="text-[44px] leading-none font-bold text-black">
            Organiser
          </p>
        </div>
      </div>
    </div>
  );
}
