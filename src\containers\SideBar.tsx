import React from "react";
import {
  faArrowUpRightFromSquare,
  faSearch,
  IconDefinition,
} from "@fortawesome/free-solid-svg-icons";
import { useLocation, useNavigate } from "react-router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { NavRoutes } from "@/constant/routes";
import FragmentUserLogo from "@/assets/images/fragment-user-logo.png";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useGetAccounDetails } from "@/api/account";
import { useAuth } from "@/context/AuthContext";

type NavButtonProps = {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  isActive: boolean;
};

type NavIconProps = {
  icon: IconDefinition;
  isActive: boolean;
};

const NavButton: React.FC<NavButtonProps> = ({
  icon,
  label,
  onClick,
  isActive,
}) => {
  return (
    <button
      onClick={onClick}
      className={`flex flex-col items-center px-1 py-2 w-full content-start ${
        isActive ? "" : "hover:scale-110 ease-out duration-200"
      }`}>
      {icon}
      {!isActive && <span className="text-[10px] text-black">{label}</span>}
      {isActive && (
        <span className="text-[30px] font-bold text-black leading-none mt-[-12px]">
          .
        </span>
      )}
    </button>
  );
};

const NavIcon: React.FC<NavIconProps> = ({ icon, isActive }) => {
  return (
    <div
      className={`flex items-center justify-center h-6 w-6 ${
        isActive ? "text-light" : "text-light/50"
      }`}>
      <FontAwesomeIcon
        className="ease-out duration-300"
        icon={icon}
        size={isActive ? "xl" : "1x"}
        color={isActive ? "#000000" : "#83899A"}
      />
    </div>
  );
};
const NavImage: React.FC<NavIconProps & { uri?: string; name: string }> = ({
  isActive,
  uri,
  name,
}) => {
  return (
    <div
      className={`flex items-center justify-center mb-0.5 h-9 w-9 ${
        isActive ? "text-light" : "text-light/50"
      }`}>
      <Avatar className={isActive ? "w-9 h-9" : "w-7 h-7"}>
        <AvatarImage src={uri} alt="P" />
        <AvatarFallback className="font-semibold">
          {name?.slice(0, 1)}
        </AvatarFallback>
      </Avatar>
    </div>
  );
};

const SideBar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navigateToPage = (path: string) => {
    navigate(path);
  };

  const { handleLogout, userId } = useAuth();

  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  const isActivePath = (path: string) => location.pathname === path;

  return (
    <div className="h-[98vh] flex flex-col items-center px-2 py-6 bg-fb-neutral-200 rounded-full w-14 min-w-14 md:w-16 md:min-w-16 lg:w-20 lg:min-w-20 overflow-y-auto overflow-hidden m-auto mx-1 md:mx-2 lg:mx-4  no-scrollbar">
      <div className="flex flex-col items-center gap-2 basis-1/4">
        <img
          className="h-10"
          src={FragmentUserLogo}
          alt="Fragment Business Logo"
        />

        <NavButton
          icon={<NavIcon icon={faSearch} isActive={isActivePath("/search")} />}
          label="Search"
          onClick={() => navigateToPage("/search")}
          isActive={isActivePath("/search")}
        />
      </div>

      <nav className="flex flex-col basis-2/4">
        {NavRoutes.map((item, index) => {
          const isActive = isActivePath(item.path);

          return (
            <NavButton
              key={index}
              icon={
                item.isIcon ? (
                  <NavIcon icon={item.icon} isActive={isActive} />
                ) : (
                  <NavImage
                    icon={item.icon}
                    isActive={isActive}
                    uri={AccountData?.accData?.[0]?.picture || ""}
                    name={AccountData?.accData?.[0]?.userName || "B"}
                  />
                )
              }
              label={item.label}
              onClick={() => navigateToPage(item.path)}
              isActive={isActive}
            />
          );
        })}
      </nav>

      <div className="basis-1/4 flex flex-col-reverse">
        <NavButton
          icon={<NavIcon icon={faArrowUpRightFromSquare} isActive={false} />}
          label="Log-out"
          onClick={handleLogout}
          isActive={false}
        />
      </div>
    </div>
  );
};

export default SideBar;
