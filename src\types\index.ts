import { ToDoStatusValue } from "@/enums";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";

export type NavRouteT = {
  icon: IconDefinition;
  label: string;
  path: string;
  isIcon: boolean;
};

export type ZoneInfo = {
  label: string;
  tzCode: string;
  name: string;
  utc: string;
};

export type AgendaItem = {
  agendaId: string;
  type: string;
  timestamp: string;
  action: string;
  under: string;
  date: string;
  userId: string;
  isReadBy: string[];
};

export type EventModelT = {
  _id: string;
  title: string;
  allDayEvent: boolean;
  start: string;
  end: string;
  repeat: boolean;
  frequency: string;
  repeatEvery: string;
  days: string[];
  repeatEndDate: string;
  location: string;
  link: string | string[];
  picture: string;
  restrict: string[];
  zone: ZoneInfo;
  attach: string | null;
  description: string;
  notifyBefore: string;
  event: boolean;
  note: boolean;
  userType: "endUser";
  textColor: string;
  deleted: boolean;
  createdAt: string;
  calendarName: string;
  calId: string;
  color: string;
  usrId: string;
  profileImage: File | null;
  restrictRSVP?: boolean;
  allowComments?: boolean;
  allowLikes?: boolean;
  Impression: []; // any
  Rsvps: []; // any
  Comments: []; // any
  Reply: []; // any
};

export type BizzEventModelT = {
  _id: string;
  businessName: string;
  calendarName: string;
  calId: string;
  logo?: string;
  title: string;
  allDayEvent: boolean;
  start: string;
  end: string;
  repeat: boolean;
  frequency: string;
  repeatEvery: number;
  days: string[];
  repeatEndDate: string;
  location: string;
  link: string;
  picture: string;
  youtube: string;
  restrict: string[];
  zone: ZoneInfo;
  attach: string;
  description: string;
  reminderSelected: string;
  reminder: string;
  personalReminder: string;
  notifyBefore: string;
  event: boolean;
  note: boolean;
  color: string;
  userType: string;
  textColor: string;
  deleted: boolean;
  blocked: boolean;
  createdAt: string;
  Impression: []; // any
  Rsvps: []; // any
  Comments: []; // any
  Reply: []; // any
  usrId: string;
  Agenda: AgendaItem[];
  restrictRSVP: boolean;
  allowComments: boolean;
  allowLikes: boolean;
};

export interface FileWithPreview extends File {
  preview: string;
}

// Single follower
export type FollowerT = {
  usrId: string;
  displayName: string;
  email: string | null;
  userId: string;
  picture: string | null;
};

// Follow history
export type FollowHistoryT = {
  usrId: string;
  date: string; // ISO date string
  action: string;
};

export type CalendarModelT = {
  _id: string;
  calendarName: string;
  color: string;
  deleted: boolean;
  userType: string;
  isDeviceCalendar: boolean;
  usrId: string;
  createdAt: string;
};

export type BizzCalendarModelT = {
  _id: string;
  businessName: string;
  businessId: string;
  bizId: string;
  calendarName: string;
  calendarId: string;
  phoneNumber: string;
  email: string | null;
  deleted: boolean;
  followers: FollowerT[];
  businessUser: boolean;
  private: boolean;
  blocked: boolean;
  catagory: string;
  subCatagory: string;
  description: string;
  usrId: string;
  createdAt: string;
  isRead?: boolean;
  followHistory?: FollowHistoryT[];
  picture?: string | null;
};

export type TodoListModelT = {
  taskId: string;
  category: string | null;
  priorityImg: ToDoStatusValue;
  task: string;
  createdAt: string;
  date: string;
  status?: ToDoStatusValue;
  updatedAt?: string;
  comment?: string;
};

export type NotificationModelT = {
  _id: string;
  type?: string;
  isRead?: boolean;
  rsvpStatus?: string;
  email?: string;
  userId?: string;
  picture?: string;
  recipientUsrId?: string;
  recipientDisplayName?: string;
  replyMakerDisplayName?: string;
  replyMakerUsrId?: string;
  recipientEmail?: string;
  recipientUserId?: string;
  rsvpMakerUsrId?: string;
  rsvpMakerDisplayName?: string;
  rsvpMakerUserId?: string;
  rsvpMakerPicture?: string;
  businessName?: string;
  businessId?: string;
  bizId?: string;
  eventDate?: string;
  createdAt?: string;
  message?: string;
  updaterUsrId?: string;
  updaterType?: string;
  calId?: string;
  calendarName?: string;
  agendaId?: string;
  eventTitle?: string;
  eventStart?: string;
  eventEnd?: string;
  eventLocation?: string;
  eventDescription?: string;
  allDayEvent?: boolean;
  isDeleted?: boolean;
  commentId?: string;
  reply?: string;
};

export type FollowingModelT = {
  cal_name: string;
  uniqId: string;
  bus_name: string;
  type: "calendar" | "bussiness";
  isUserFolllowing: boolean;
  imageUri: string;
};

export type UserAccountResponseT = {
  userData: UserDataT;
  accData: AccountDataT[];
};

export type UserDataT = {
  _id: string;
  displayName: string;
  email: string;
  uid: string | null;
  isAnonymous: boolean;
  providerId: string;
  following?: string[];
  populateAgendaFollowing?: string[];
};

export type ReportItemT = {
  userId: string;
  message: string;
};

export type AccountDataT = {
  _id: string;
  userName: string;
  userId: string;
  gender: string;
  dob: string;
  interest: string[];
  picture?: string;
  website: string;
  email: string;
  instagram: string;
  linkedIn: string;
  deleted: boolean;
  blocked: boolean;
  userType: "endUser";
  usrId: string;
  Tasks?: TodoListModelT[];
  Category?: string[];
  pictureImage?: File;
};

export type CommentModelT = {
  commentId: string;
  userId: string;
  displayName: string;
  date: string;
  comment: string;
  timeStmp: string;
  type: string;
  picture: string | null;
  report?: boolean;
  reportCount?: number;
  reportReasons?: string[];
  reportedBy?: string[];
};

export type SubCommentModelT = {
  replyId: string;
  commentId: string;
  userId: string;
  displayName: string;
  date: string;
  reply: string;
  timeStmp: string;
  type: string;
  picture: string | null;
  report?: boolean;
  reportCount?: number;
  reportReasons?: string[];
  reportedBy?: string[];
};

export type UploadImageResponseT = {
  location: string;
  publicUrl: string;
  key: string;
  mimetype: string;
  size: number;
  sizeInMB: string;
  originalName: string;
  uploadedAt: string;
};
