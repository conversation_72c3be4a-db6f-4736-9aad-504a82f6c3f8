import { formatEventDate } from "@/utils";
import { NotificationModelT } from "@/types";
import { useAuth } from "@/context/AuthContext";
import { useGetAllNotifications } from "@/api/notification";
import AppLoader from "@/components/common/app-loader";

type GroupedData = {
  date: string;
  data: NotificationModelT[];
};

const groupNotificationsByDate = (
  datas: NotificationModelT[]
): GroupedData[] => {
  return datas.reduce<GroupedData[]>((acc, event) => {
    const dateKey = new Date(event.createdAt || "").toDateString();

    const existingGroup = acc.find((group) => group.date === dateKey);

    if (existingGroup) {
      existingGroup.data.push(event);
    } else {
      acc.push({ date: dateKey, data: [event] });
    }

    return acc;
  }, []);
};

export default function NotificaitonsDayWise() {
  const { userId } = useAuth();

  const { data = null, isLoading } = useGetAllNotifications({ usrId: userId });

  const newData = data?.notifications || [];

  const groupNotifications = groupNotificationsByDate(newData || []);

  return (
    <div className="flex flex-col gap-3 overflow-y-auto no-scrollbar px-3 lg:px-6">
      {isLoading ? (
        <AppLoader />
      ) : (
        groupNotifications.map((row, groupIdx) => {
          const date = formatEventDate(row.date);
          return (
            <div key={groupIdx} className="flex flex-col gap-2 mb-1">
              <div className="flex gap-1 items-end border-b border-fb-bPrime-hgts">
                <p className="font-semibold text-base">{date.date},</p>
                <p className="font-light text-xs">({date.day})</p>
              </div>
              {row.data.map((not, notifIdx) => {
                return <NotificationView data={not} key={notifIdx} />;
              })}
            </div>
          );
        })
      )}
    </div>
  );
}

const NotificationView = ({ data }: { data: NotificationModelT }) => {
  const {
    createdAt: date,
    reply,
    picture,
    recipientDisplayName,
    message,
  } = data;

  const newDate = new Date(date || ""); // Replace with your date
  const time = newDate.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  return (
    <div className="flex flex-col md:flex-row gap-0.5 md:gap-3 w-full">
      <div className="md:py-2 ml-auto flex h-full items-end">
        <p className="text-xs -tracking-wide whitespace-nowrap">{time}</p>
      </div>
      <div className="p-2 px-3 rounded-xl flex w-full gap-4 bg-fb-neutral-50 shadow-cardInnerShadow items-center drop-shadow-cardShadow">
        <div className="w-9 h-9 min-w-9 min-h-9 lg:w-10 lg:h-10 rounded-full overflow-hidden ">
          <img
            src={picture}
            className="w-full h-full object-cover rounded-full"
          />
        </div>
        <div>
          <div className="flex items-center gap-2">
            <p className="text-xs lg:text-sm font-medium leading-4">
              {recipientDisplayName} {"  "}
            </p>
            {/* <ChevronDown className="size-4" /> */}
          </div>
          <p className="text-xxs md:text-xs text-black">{message}</p>
          <p className="text-xxs md:text-xs text-black">{reply}</p>
        </div>
      </div>
    </div>
  );
};
