import { useMemo, useState } from "react";

import { Button } from "@/components/ui/button";
import { filterOngoingDateEvents, filterOngoingDateTodos } from "@/utils";
import SquarePlus from "@/assets/svg/square-plus-icon.svg";
import MoodNoteBlue from "@/components/common/mood-note-blue";
import AddEventSmallView from "@/components/common/add-event-small";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import {
  BizzEventMiniCardView,
  EventMiniCardView,
  TodoCardView,
} from "@/components/common/event/card-view";
import { EventDateChangeComponent } from "@/components/common/event/date-change-comp";
import { useAuth } from "@/context/AuthContext";

export default function EventsOnDateView() {
  const [date, setDate] = useState(new Date());

  const { userId } = useAuth();

  const { events, TodoList } = useCalendarsWithEvents(userId);

  const [isAddEvent, setIsAddEvent] = useState(false);

  const { filterEventsData, filterTodosData } = useMemo(() => {
    const filterEventsData = filterOngoingDateEvents(events, date);

    const filterTodosData = filterOngoingDateTodos(TodoList, date);
    // const filterTodosData = TodoList;

    return { filterEventsData, filterTodosData };
  }, [events, date]);

  return !isAddEvent ? (
    <div className="flex flex-col gap-4 w-full overflow-y-auto no-scrollbar h-full">
      {/* date */}
      <EventDateChangeComponent date={date} setDate={setDate} />

      {/* events */}
      {filterEventsData.length === 0 && filterTodosData.length === 0 ? (
        <div className="flex flex-1 flex-col gap-2 justify-center items-center px-5">
          <MoodNoteBlue text="You do not have any events yet" />
          <Button
            className="bg-fb-bPrime-600 h-8 w-full"
            onClick={() => setIsAddEvent(true)}>
            Create One
          </Button>
        </div>
      ) : (
        <div className="flex w-full flex-col gap-2">
          <Button
            size={"sm"}
            onClick={() => setIsAddEvent(true)}
            className="bg-fb-uPrime-500 text-xs py-1 text-black hover:bg-sky-400 rounded-full px-4 h-7 drop-shadow-buttonShadow">
            <img src={SquarePlus} alt="" className="w-4 h-4" />
            Add event
          </Button>
          {filterTodosData.map((row, index) => {
            return <TodoCardView {...row} key={index} />;
          })}
          {filterEventsData.map((row, index) => {
            if ("businessName" in row) {
              return <BizzEventMiniCardView {...row} key={index} />;
            } else {
              return <EventMiniCardView {...row} key={index} />;
            }
          })}
        </div>
      )}
    </div>
  ) : (
    <AddEventSmallView onCloseMainView={() => setIsAddEvent(false)} />
  );
}
