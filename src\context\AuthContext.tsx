import { toast } from "sonner";
import { useNavigate } from "react-router";
import { onAuthStateChanged, signOut, User } from "firebase/auth";
import { createContext, useEffect, useState, useContext } from "react";

import { auth } from "@/firebase";
import AppLoader from "@/components/common/app-loader";
import { DummyUserID } from "@/constant/dummy";

const AuthContext = createContext<{
  user: User | null;
  handleLogout: () => void;
  userId: string | null;
}>({
  user: null,
  handleLogout: () => {},
  userId: null,
});

export const useAuth = () => useContext(AuthContext);

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [userId] = useState<string | null>(DummyUserID);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });
    return unsubscribe;
  }, []);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigate("/login");
      toast.success("Logged out successfully!");
    } catch (error) {
      console.error("Failed to log out", error);
      toast.error("Failed to log out!");
    }
  };

  if (loading) return <AppLoader />;

  return (
    <AuthContext.Provider value={{ user, handleLogout, userId }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
