import * as React from "react";
import { Check } from "lucide-react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { groupTimeZones } from "@/utils";
import { Button } from "@/components/ui/button";

const groupedTimeZones = groupTimeZones();

export function TimezonList({
  value,
  setValue,
}: {
  value: string;
  setValue: (val: string) => void;
}) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          className="w-auto h-5 justify-between rounded-xl text-xxs md:text-xs">
          {value
            ? Object.entries(groupedTimeZones)
                .find(([_, zones]) =>
                  zones.find((zone) => zone.value === value)
                )?.[1]
                .find((tim) => tim.value === value)?.name
            : "Select Timezone..."}
          {/* <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" /> */}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="min-w-52 p-0">
        <Command>
          <CommandInput placeholder="Search Timezone..." />
          <CommandList>
            <CommandEmpty>No timezone found.</CommandEmpty>
            {Object.entries(groupedTimeZones).map((timeZone, index) => (
              <CommandGroup key={index}>
                <p className="p-0.5 font-bold">{timeZone[0]}</p>
                {timeZone[1].map((tim, ind) => {
                  return (
                    <CommandItem
                      key={ind}
                      value={tim.value}
                      onSelect={(currentValue) => {
                        setValue(currentValue === value ? "" : currentValue);
                        setOpen(false);
                      }}>
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === tim.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {tim.name}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
