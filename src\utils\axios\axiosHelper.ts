import { AxiosError, AxiosResponse } from "axios";

import { ApiErrorResponse, ApiResponse, StatusCodes } from "@/types/api";

export function handleApiError(
  error: unknown,
  defaultMessage: string
): ApiErrorResponse {
  console.error(error, defaultMessage);

  let message = StatusCodes.SERVER_ERROR
    ? "Server error..."
    : defaultMessage || "Something went wrong..";
  let status = StatusCodes.SERVER_ERROR;

  if (error instanceof AxiosError) {
    status = error.response?.status || StatusCodes.SERVER_ERROR;

    if (error.response?.data && typeof error.response.data === "object") {
      message =
        (error.response.data as { error?: string })?.error ||
        (error.response.data as { message?: string })?.message ||
        message;
    }
  }

  if (status === StatusCodes.UNAUTHORIZED) {
    return {
      data: null,
      success: false,
      message: "Unauthorized access...",
      status: StatusCodes.UNAUTHORIZED,
    };
  }

  return {
    data: null,
    success: false,
    message,
    status,
  };
}

// Helper to handle API responses
export function handleApiResponse<T>(
  response: AxiosResponse,
  successStatus: StatusCodes,
  failureMessage: string
): ApiResponse<T> {
  if (response.status === successStatus) {
    // console.log(response.data, response.status, "Api response");
    return {
      data: (response.data && typeof response.data === "object"
        ? response.data.data
        : null) as T,
      success: true,
      message:
        typeof response.data === "object" && "message" in response.data
          ? response.data.message
          : "Data Fetched...",
      status: response.status,
    };
  }

  // console.error(failureMessage, response.data, "Api response");
  return {
    data: null,
    success: false,
    message:
      typeof response.data === "object" && "message" in response.data
        ? response.data.message
        : failureMessage,
    status: response.status || StatusCodes.SERVER_ERROR,
  };
}
