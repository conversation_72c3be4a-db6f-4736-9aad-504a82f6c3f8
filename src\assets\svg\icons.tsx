export const PdfIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="33"
      height="33"
      viewBox="0 0 33 33"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.83203 8.9681C5.83203 6.02258 8.21985 3.63477 11.1654 3.63477H16.4987H19.2497C20.0411 3.63477 20.7916 3.98629 21.2983 4.59427L26.5473 10.8931C26.9467 11.3723 27.1654 11.9764 27.1654 12.6002V16.9681V24.9681C27.1654 27.9136 24.7775 30.3014 21.832 30.3014H11.1654C8.21985 30.3014 5.83203 27.9136 5.83203 24.9681V8.9681Z"
        stroke="currentColor"
        strokeWidth="2"
      />
      <path
        d="M20.498 4.30176V8.96842C20.498 10.4412 21.692 11.6351 23.1647 11.6351H26.498"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M10.7513 19.4414H11.2161C11.6506 19.4414 11.9757 19.3563 12.1914 19.1862C12.4071 19.013 12.515 18.7624 12.515 18.4342C12.515 18.1031 12.4238 17.8585 12.2415 17.7005C12.0623 17.5425 11.7797 17.4635 11.3939 17.4635H10.7513V19.4414ZM13.9414 18.3841C13.9414 19.1011 13.7166 19.6495 13.2669 20.0293C12.8203 20.4091 12.1838 20.599 11.3574 20.599H10.7513V22.9688H9.33854V16.306H11.4668C12.275 16.306 12.8887 16.4807 13.3079 16.8301C13.7303 17.1764 13.9414 17.6944 13.9414 18.3841ZM16.5165 23.0599C15.9179 23.0599 15.447 22.8275 15.1037 22.3626C14.7634 21.8978 14.5933 21.2537 14.5933 20.4303C14.5933 19.5948 14.7665 18.9447 15.1128 18.4798C15.4622 18.0119 15.9422 17.778 16.5529 17.778C17.194 17.778 17.6831 18.0271 18.0204 18.5254H18.0659C17.9961 18.1456 17.9611 17.8069 17.9611 17.5091V15.8776H19.3557V22.9688H18.2892L18.0204 22.3079H17.9611C17.6451 22.8092 17.1636 23.0599 16.5165 23.0599ZM17.0041 21.9525C17.3596 21.9525 17.6193 21.8492 17.7834 21.6426C17.9505 21.436 18.0416 21.0851 18.0568 20.5898V20.4395C18.0568 19.8926 17.9718 19.5007 17.8016 19.2637C17.6345 19.0267 17.3611 18.9082 16.9813 18.9082C16.6714 18.9082 16.4299 19.0404 16.2567 19.3047C16.0866 19.566 16.0015 19.9473 16.0015 20.4486C16.0015 20.9499 16.0881 21.3266 16.2613 21.5788C16.4344 21.8279 16.682 21.9525 17.0041 21.9525ZM23.4392 18.9173H22.236V22.9688H20.8461V18.9173H20.0804V18.2474L20.8461 17.8737V17.5C20.8461 16.9197 20.9889 16.4959 21.2745 16.2285C21.56 15.9612 22.0173 15.8275 22.6462 15.8275C23.1262 15.8275 23.5531 15.8989 23.9268 16.0417L23.5713 17.0625C23.2918 16.9744 23.0336 16.9303 22.7966 16.9303C22.5991 16.9303 22.4563 16.9896 22.3682 17.1081C22.2801 17.2235 22.236 17.3724 22.236 17.5547V17.8737H23.4392V18.9173Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TodoSmileIcon = ({ className }: { className: string }) => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}>
      <path
        d="M15.9974 30.3024C23.3612 30.3024 29.3307 24.3329 29.3307 16.9691C29.3307 9.60528 23.3612 3.63574 15.9974 3.63574C8.6336 3.63574 2.66406 9.60528 2.66406 16.9691C2.66406 24.3329 8.6336 30.3024 15.9974 30.3024Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.65625 22.3018C11.0562 24.3151 13.4029 25.6351 16.0429 25.6351C18.6829 25.6351 21.0162 24.3151 22.4296 22.3018"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SquarePlus = ({ className }: { className?: string }) => {
  return (
    <svg
      width="33"
      height="33"
      viewBox="0 0 33 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}>
      <rect
        width="26.6667"
        height="26.6667"
        rx="6.66667"
        transform="matrix(-1 0 0 1 29.832 3.63574)"
        stroke="currentColor"
        strokeWidth="2"
      />
      <path
        d="M20.498 16.9692H12.498"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.498 12.9692L16.498 20.9692"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
