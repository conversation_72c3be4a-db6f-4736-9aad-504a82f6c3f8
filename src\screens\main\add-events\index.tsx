import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { SearchIcon, X } from "lucide-react";

import { EventModelT, FileWithPreview } from "@/types";
import { Input } from "@/components/ui/input";
import { Image } from "@/components/ui/image";
import { Button } from "@/components/ui/button";
import AchiveIcon from "@/assets/svg/achive-icon.svg";
import PlaceholderImg from "@/assets/images/add-placeholder.jpg";
import BussinessInfo from "@/components/common/bussiness-info";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DateTimePicker } from "@/components/common/date-time-picker";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import LocationImage from "@/assets/images/warsaw.png";
import InfoIcon from "@/assets/svg/Info-circle-icon.svg";
import {
  currentTimeZone,
  generateUUID,
  getPreviousHalfHour,
  getZoneFromTzCode,
} from "@/utils";
import { ConformationPopup } from "@/components/common/conformation-popup";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import { useAuth } from "@/context/AuthContext";
import { useNavigate } from "react-router";
import { addHours } from "date-fns";
import { useCreateEvent } from "@/api/event";
import { FILE_TYPES, useCustomDropzone } from "@/hook/useCustomDropzone";

const ReminderEventTime = [
  { name: "5 Mins", value: "5-min" },
  { name: "10 Mins", value: "10-min" },
  { name: "15 Mins", value: "15-min" },
  { name: "30 Mins", value: "30-min" },
  { name: "1 Hour", value: "1-hour" },
  { name: "1 Day", value: "1-day" },
];

const RepeatEventTime = [
  { name: "Never", value: "never" },
  { name: "Daily", value: "daily" },
  { name: "Weekly", value: "weekly" },
  { name: "Monthly", value: "monthly" },
];

export default function AddEventWithEnterScreen() {
  const { mutate, isPending } = useCreateEvent();

  const { userId } = useAuth();
  const navigate = useNavigate();

  const { apiCalendars } = useCalendarsWithEvents(userId);

  const [eventImage, setEventImage] = useState<FileWithPreview[]>([]);
  const [attachmentPdf, setAttachmentPdf] = useState<FileWithPreview[]>([]);

  const [eventName, setEventName] = useState("");
  const [isAllDayEvent, setIsAllDayEvent] = useState(false);
  const [chooseCalendar, setChooseCalendar] = useState<string | null>(null);
  const [reminderTime, setReminderTime] = useState<string | null>(null);
  const [repeatTime, setRepeatTime] = useState<string | null>(null);

  const [attachedLinks, setAttachedLinks] = useState<string[]>([""]);

  const [provideRSVP, setProvideRSVP] = useState(false);
  const [allowComments, setAllowComments] = useState(false);
  const [allowLikes, setAllowLikes] = useState(false);

  const [startDate, setStartDate] = useState<Date>(() => getPreviousHalfHour());
  const [endDate, setEndDate] = useState<Date>(() => {
    const start = getPreviousHalfHour();
    start.setHours(start.getHours() + 1);
    return start;
  });

  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");

  const [conformationPopup, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...attachedLinks];
    newLinks[index] = value;
    setAttachedLinks(newLinks);
  };

  const handleLinkRemove = (index: number) => {
    const updated = attachedLinks.filter((_, i) => i !== index);
    setAttachedLinks(updated);
  };

  const handleAddLink = () => {
    setAttachedLinks([...attachedLinks, ""]);
  };

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for event cover",
      "file-too-large": "Event image is too large",
      "too-many-files": "You can only upload one event image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setEventImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // No onFilesRejected - errors handled automatically
  });

  const { getRootProps: getRootPropsOfPdf, getInputProps: getInputPropsOfPdf } =
    useCustomDropzone({
      acceptedFileTypes: FILE_TYPES.DOCUMENTS,
      maxFiles: 1,
      maxSize: 1 * 1024 * 1024, // 1MB
      multiple: false,
      showToastOnError: true,
      customErrorMessages: {
        "file-invalid-type": "Please upload only PDF files",
        "file-too-large": "PDF file is too large",
        "too-many-files": "You can only upload one PDF file",
      },
      onFilesAccepted: (acceptedFiles) => {
        setAttachmentPdf(
          acceptedFiles.map((file) =>
            Object.assign(file, {
              preview: URL.createObjectURL(file),
            })
          )
        );
      },
      // No onFilesRejected - errors handled automatically
    });

  const handleCreateEvent = (e: FormEvent) => {
    e.preventDefault();

    if (!chooseCalendar || !apiCalendars) {
      toast.info("Calendar is required");
      return;
    }

    const calendar = apiCalendars.find((cal) => cal._id === chooseCalendar);

    const FormData: EventModelT = {
      _id: generateUUID("agenda"),
      calId: chooseCalendar,
      calendarName: calendar?.calendarName || "",
      end: endDate.toISOString(),
      start: startDate.toISOString(),
      event: true,
      note: false,
      title: eventName,
      userType: "endUser",
      usrId: userId || "",
      zone: getZoneFromTzCode(currentTimeZone),
      allDayEvent: isAllDayEvent,
      deleted: false,
      location,
      description,
      frequency: repeatTime || "",
      days: repeatTime ? [repeatTime] : [],
      repeat: repeatTime != "Never",
      repeatEndDate: endDate.toISOString(),
      repeatEvery: repeatTime || "",
      picture: "",
      link: attachedLinks,
      color: calendar?.color || "#c0c3cb ",
      attach: null,
      textColor: "#454343",
      profileImage: eventImage[0] || null,
      notifyBefore: reminderTime || "",
      restrictRSVP: provideRSVP,
      allowComments: allowComments,
      allowLikes: allowLikes,
      restrict: [],
      createdAt: new Date().toISOString(),
      Comments: [],
      Impression: [],
      Reply: [],
      Rsvps: [],
    };

    console.log(FormData, "--- creat event formData--");
    mutate(FormData, {
      onSuccess() {
        handleSetConformation({
          title: "Event created",
          subTitle: "",
        });
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  const handleClosePopups = () => {
    setConformationPopup({ open: false, title: "", subTitle: "" });
    navigate(-1);
  };

  return (
    <div className="flex flex-col w-full h-dvh pr-3">
      <div className="justify-end flex items-end">
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <form
        onSubmit={handleCreateEvent}
        className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto gap-3">
        {/* side bar */}
        <div className="w-80 lg:w-96 min-w-80 lg:min-w-96 flex flex-col p-6 gap-3 overflow-y-auto no-scrollbar bg-white rounded-2xl">
          <div className="px-2  flex items-center justify-between">
            <p className="text-black font-bold text-xl">New Event</p>
            <button type="button" onClick={() => navigate(-1)}>
              <img src={AchiveIcon} className="size-5" />
            </button>
          </div>
          <div className="flex flex-col gap-1">
            <Label htmlFor="EventName" className="text-fb-neutral-400 pl-3">
              Event Name
            </Label>

            <Input
              required
              id="EventName"
              value={eventName}
              onChange={(e) => {
                setEventName(e.target.value);
              }}
              className="!rounded-12px border-fb-neutral-400"
              placeholder="Name"
            />
          </div>
          <div className="flex flex-col gap-1 border-b border-fb-neutral-200">
            <Label className="text-fb-neutral-400 pl-3 ">Choose Calendar</Label>
            <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-1">
              {apiCalendars?.map((cal, ind) => {
                return (
                  <div
                    onClick={() => {
                      setChooseCalendar(cal._id);
                    }}
                    key={ind}
                    className={cn(
                      " border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                      chooseCalendar === cal._id
                        ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                        : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                    )}
                    style={{ backgroundColor: `${cal.color}50` }}>
                    <div
                      className="w-3 h-3 min-w-3 rounded-full bg-fb-bPrime-500"
                      style={{ backgroundColor: cal.color }}
                    />
                    <p className="truncate text-xs font-semibold">
                      {cal.calendarName}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Label htmlFor="all-day">All day</Label>
            <Switch
              checked={isAllDayEvent}
              onCheckedChange={(e) => {
                setIsAllDayEvent(e);
              }}
              id="all-day"
              size="md"
            />
          </div>
          <div className="w-full flex flex-col gap-0.5 items-center justify-center">
            <div className="flex gap-1 items-center">
              <DateTimePicker
                date={startDate}
                setDate={(e) => {
                  setStartDate(e);
                  setEndDate(addHours(e, 1));
                }}
                isSmallView
                showDay={false}
              />
              <div className="w-12 h-0.5 rounded-full bg-black/70" />
              <DateTimePicker
                date={endDate}
                setDate={(e) => {
                  if (e.getTime() < startDate.getTime()) {
                    toast.info("End date cannot be before start date/time");
                    return;
                  }
                  setEndDate(e);
                }}
                isSmallView
                showDay={false}
              />
            </div>
          </div>
          <div>
            <Accordion type="single" collapsible>
              <AccordionItem value="item-1">
                <AccordionTrigger className="justify-end gap-2 text-sm text-fb-neutral-400 p-0">
                  More Categories
                </AccordionTrigger>
                <AccordionContent></AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
        {/*  views */}
        <div className="flex flex-col flex-1 gap-2 ">
          <div className="flex flex-col gap-2 p-6 w-full h-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
            <div className="flex justify-end gap-1 ">
              <Button
                onClick={() => navigate(-1)}
                size={"sm"}
                variant={"ghost"}
                type="button"
                className="rounded-full h-7 text-fb-warn-500">
                Cancel
                <X className="size-4 text-black" />
              </Button>
            </div>
            <div className="grid grid-cols-7 items-center">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Reminder before event :
                </p>
              </div>
              <div className="col-span-5 grid grid-cols-6 gap-1">
                {ReminderEventTime.map((remind, ind) => {
                  return (
                    <div
                      className={cn(
                        "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                        reminderTime === remind.value
                          ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                          : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                      )}
                      key={ind}
                      onClick={() => {
                        setReminderTime(remind.value);
                      }}>
                      {remind.name}
                    </div>
                  );
                })}
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Add Image :
                </p>
              </div>
              <div className="col-span-5 gap-1">
                <div className="flex flex-col w-full">
                  {eventImage.length > 0 ? (
                    <div className="w-full h-24 rounded-12px overflow-hidden relative">
                      <Image
                        src={eventImage[0]?.preview}
                        fallbackSrc={PlaceholderImg}
                      />
                      <Button
                        type="button"
                        variant={"ghost"}
                        size={"icon"}
                        className="absolute top-2 right-2 h-6 w-6"
                        onClick={() => setEventImage([])}>
                        <X className="text-fb-warn-600" />
                      </Button>
                    </div>
                  ) : (
                    <div
                      {...getRootProps({ className: "dropzone" })}
                      className="bg-fb-neutral-200 rounded-12px overflow-hidden h-24 flex justify-center items-center cursor-pointer relative">
                      <input {...getInputProps()} />
                      <Image src={PlaceholderImg} />
                      <div className="bg-fb-bPrime-placeholder/50 absolute top-0 left-0 w-full h-full flex justify-center items-center">
                        <p className="text-lg font-semibold">Add Image</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Description :
                </p>
              </div>
              <div className="col-span-5 gap-1">
                <Textarea
                  value={description}
                  onChange={(e) => {
                    setDescription(e.target.value);
                  }}
                  className="!rounded-12px border-fb-neutral-400 resize-none"
                  placeholder="Description"
                />
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7 items-center">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Repeat this event :
                </p>
              </div>
              <div className="col-span-5 grid grid-cols-4 gap-1">
                {RepeatEventTime.map((remind, ind) => {
                  return (
                    <div
                      className={cn(
                        "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                        repeatTime === remind.value
                          ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                          : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                      )}
                      key={ind}
                      onClick={() => {
                        setRepeatTime(remind.value);
                      }}>
                      {remind.name}
                    </div>
                  );
                })}
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Location :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className="flex items-center relative">
                  <Input
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="!rounded-12px border-fb-neutral-400 pr-8"
                    placeholder="Location"
                  />
                  <SearchIcon className="absolute right-3 size-4 text-fb-neutral-600" />
                </div>
                <div className="overflow-hidden rounded-12px h-28 w-full border border-fb-neutral-400">
                  <Image
                    src={LocationImage}
                    className="contrast-75 opacity-30"
                  />
                </div>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Add Links :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                {attachedLinks.map((links, ind) => {
                  return (
                    <div className="relative flex items-center" key={ind}>
                      <Input
                        key={ind}
                        value={links}
                        type="url"
                        className="!rounded-12px border-fb-neutral-400 pr-8"
                        placeholder="Enter Link"
                        onChange={(e) => {
                          handleLinkChange(ind, e.target.value);
                        }}
                      />
                      <Button
                        type="button"
                        variant={"ghost"}
                        size={"icon"}
                        className="absolute right-1 !h-7 !w-7 rounded-full"
                        onClick={() => handleLinkRemove(ind)}>
                        <X />
                      </Button>
                    </div>
                  );
                })}
                <Button
                  type="button"
                  variant={"outline"}
                  className="!rounded-12px border-fb-neutral-400 text-fb-neutral-600"
                  onClick={handleAddLink}>
                  Add a New Link
                </Button>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Attach Documents :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                {attachmentPdf.length > 0 ? (
                  <div>
                    <Button
                      size={"sm"}
                      variant={"ghost"}
                      type="button"
                      className="rounded-full h-7 text-fb-neutral-600"
                      onClick={() => {
                        setAttachmentPdf([]);
                      }}>
                      <X className="size-4" />
                      {attachmentPdf[0].name}
                    </Button>
                  </div>
                ) : (
                  <div
                    {...getRootPropsOfPdf()}
                    className="w-28 min-w-36 lg:min-w-64 bg-fb-neutral-200 rounded-xl flex flex-col h-full justify-center items-center gap-2 relative cursor-pointer">
                    <input {...getInputPropsOfPdf()} />

                    <p className="text-lg font-light text-fb-neutral-600">
                      Attach Pdf
                    </p>
                  </div>
                )}
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  User Interactions :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="option-RSVP"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Provide RSVP
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch
                    id="option-RSVP"
                    size="md"
                    checked={provideRSVP}
                    onCheckedChange={(e) => {
                      setProvideRSVP(e);
                    }}
                  />
                </div>
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="allow-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Allow Comments
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch
                    id="allow-comments"
                    size="md"
                    checked={allowComments}
                    onCheckedChange={(e) => {
                      setAllowComments(e);
                    }}
                  />
                </div>
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="RSVP-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 cursor-pointer text-fb-neutral-600">
                    Allow Users see likes
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch
                    id="RSVP-comments"
                    size="md"
                    checked={allowLikes}
                    onCheckedChange={(e) => {
                      setAllowLikes(e);
                    }}
                  />
                </div>
              </div>
            </div>
            <Separator />
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isPending}
                className="bg-fb-bPrime-600 !rounded-12px w-80">
                Add Event
              </Button>
            </div>
          </div>
        </div>
      </form>

      <ConformationPopup
        isOpen={conformationPopup.open}
        title={conformationPopup.title}
        subTitle={conformationPopup.subTitle}
        onCLose={handleClosePopups}
      />
    </div>
  );
}
