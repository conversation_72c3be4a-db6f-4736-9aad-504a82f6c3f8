import { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight, Flame } from "lucide-react";
import {
  format,
  addDays,
  startOfWeek,
  addWeeks,
  subWeeks,
  isSameDay,
  isToday,
  subMonths,
  addMonths,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
} from "date-fns";

import { cn } from "@/lib/utils";
import {
  filterOngoingDateEvents,
  filterOngoingDateTodos,
  formatTimeOnly,
  timeSlots,
} from "@/utils";
import { BizzEventModelT, EventModelT, TodoListModelT } from "@/types";
import BgImage from "@/assets/images/bg-image.png";
import { ToDoStatusValue } from "@/enums";

const CELL_HEIGHT = 44;
const HEADER_HEIGHT = 50;
const TODO_CELL_HEIGHT = 26;

const getTodaysTodos = (todoList: TodoListModelT[], date: Date | string) => {
  const listOfData = filterOngoingDateTodos(todoList, date);

  return { todoList: listOfData, height: TODO_CELL_HEIGHT * listOfData.length };
};

// Add the current time indicator functionality to the component
export default function WeeklyCalendar({
  events,
  Todos,
}: {
  events: (EventModelT | BizzEventModelT)[];
  Todos: TodoListModelT[];
}) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekStart, setWeekStart] = useState(
    startOfWeek(currentDate, { weekStartsOn: 1 })
  );
  const [now, setNow] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [pickerDate, setPickerDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const timeIndicatorRef = useRef<HTMLDivElement>(null);

  // Update week start when current date changes
  useEffect(() => {
    setWeekStart(startOfWeek(currentDate, { weekStartsOn: 1 }));
  }, [currentDate]);

  useEffect(() => {
    // Scroll to the timeIndicatorRef element when the component is mounted
    if (timeIndicatorRef.current) {
      timeIndicatorRef.current.scrollIntoView({
        behavior: "smooth", // Smooth scrolling
        block: "center", // Align the element to the center of the viewport
      });
    }
  }, []);

  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(new Date());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Handle date picker submission
  const handleDateSubmit = () => {
    if (selectedDate) {
      setCurrentDate(selectedDate);
      setShowDatePicker(false);
      setSelectedDate(null);
    }
  };

  // Handle date selection in picker
  const handleDateSelect = (date: Date) => {
    // setSelectedDate(date);
    setCurrentDate(date);
    setShowDatePicker(false);
  };

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentDate(subWeeks(currentDate, 1));
  };

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentDate(addWeeks(currentDate, 1));
  };

  // Navigate picker to previous month
  const goToPreviousMonth = () => {
    setPickerDate(subMonths(pickerDate, 1));
  };

  // Navigate picker to next month
  const goToNextMonth = () => {
    setPickerDate(addMonths(pickerDate, 1));
  };

  // Get the starting day of the week for the first day of the month
  const getMonthStartDay = () => {
    const monthStart = startOfMonth(pickerDate);
    const dayOfWeek = monthStart.getDay();
    return dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday=0 to Monday=0 format
  };

  // Handle keyboard shortcuts
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleDateSubmit();
    } else if (e.key === "Escape") {
      setShowDatePicker(false);
      setSelectedDate(null);
    }
  };

  // Generate calendar days for date picker
  const getCalendarDays = () => {
    const monthStart = startOfMonth(pickerDate);
    const monthEnd = endOfMonth(pickerDate);

    return eachDayOfInterval({ start: monthStart, end: monthEnd });
  };

  // Filter MemoEvents for the current week
  const getEventsForDay = (day: Date) => {
    if (events) {
      return filterOngoingDateEvents(events, day);
    } else {
      return [];
    }
  };

  const getCurrentTimeIndex = () => {
    const hours = now.getHours(); // Get current hour (0-23)

    return hours; // Return index corresponding to timeSlots
  };

  // current time is passed or not for time index
  const currentTimeIndex = getCurrentTimeIndex();

  // Generate days of the week
  const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  const maxTodoHeight = Math.max(
    ...days.map((day) => getTodaysTodos(Todos, day).height)
  );

  // Calculate current time indicator position
  const getCurrentTimePosition = () => {
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const timeInHours = hours + minutes / 60;

    // Calculate position relative to 0 AM start
    if (timeInHours < 0) return 0;
    if (timeInHours > 23) return 23 * 60;

    return timeInHours * CELL_HEIGHT + HEADER_HEIGHT + 6 + maxTodoHeight; // cell height per hour + header hight + padding
  };

  return (
    <div className="overflow-hidden flex flex-col">
      {/* Calendar Header */}
      <div className="flex items-center mx-auto gap-2 w-fit relative">
        <button
          onClick={goToPreviousWeek}
          className="p-0.5 rounded-full hover:bg-gray-100">
          <ChevronLeft className="h-4 w-4" />
        </button>
        <button
          onClick={() => {
            setShowDatePicker(true);
            setPickerDate(currentDate);
            setSelectedDate(currentDate);
          }}
          className="p-0.5 rounded-full hover:bg-gray-100 flex items-center gap-2"
          title="Jump to date">
          <h2 className="text-sm font-semibold">
            {format(weekStart, "MMMM yyyy")}
          </h2>
        </button>

        <button
          onClick={goToNextWeek}
          className="p-0.5 rounded-full hover:bg-gray-100">
          <ChevronRight className="h-4 w-4" />
        </button>

        {/* Date Picker Popup */}
        {showDatePicker && (
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50">
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-[320px]">
              <div className="flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900">
                    Select Date
                  </h3>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={goToPreviousMonth}
                      className="p-1 rounded-full hover:bg-gray-100">
                      <ChevronLeft className="h-4 w-4" />
                    </button>
                    <span className="text-sm font-medium min-w-[120px] text-center">
                      {format(pickerDate, "MMMM yyyy")}
                    </span>
                    <button
                      onClick={goToNextMonth}
                      className="p-1 rounded-full hover:bg-gray-100">
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {/* Day headers */}
                  {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(
                    (day) => (
                      <div
                        key={day}
                        className="text-xs text-gray-500 text-center py-2 font-medium">
                        {day}
                      </div>
                    )
                  )}

                  {/* Empty cells for days before month starts */}
                  {Array.from({ length: getMonthStartDay() }, (_, index) => (
                    <div key={`empty-${index}`} className="w-8 h-8"></div>
                  ))}

                  {/* Calendar days */}
                  {getCalendarDays().map((day, index) => {
                    const isSelected =
                      selectedDate && isSameDay(day, selectedDate);
                    const isTodayDate = isToday(day);

                    return (
                      <button
                        key={index}
                        onClick={() => handleDateSelect(day)}
                        onKeyDown={handleKeyPress}
                        className={cn(
                          "w-8 h-8 text-xs rounded-full transition-colors",
                          "hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500",
                          "text-gray-700",
                          isTodayDate &&
                            !isSelected &&
                            "bg-blue-50 text-blue-600 font-medium",
                          isSelected && "bg-blue-500 text-white font-medium"
                        )}>
                        {format(day, "d")}
                      </button>
                    );
                  })}
                </div>

                {/* <div className="flex gap-2 justify-end pt-2 border-t border-gray-100">
                  <button
                    onClick={() => {
                      setShowDatePicker(false);
                      setSelectedDate(null);
                    }}
                    className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                    Cancel
                  </button>
                  <button
                    onClick={handleDateSubmit}
                    disabled={!selectedDate}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-md transition-colors",
                      selectedDate
                        ? "bg-blue-500 text-white hover:bg-blue-600"
                        : "bg-gray-100 text-gray-400 cursor-not-allowed"
                    )}>
                    Go to Date
                  </button>
                </div> */}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Backdrop for closing popup */}
      {showDatePicker && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowDatePicker(false);
            setSelectedDate(null);
          }}
        />
      )}

      {/* Calendar Grid */}
      <div className="relative overflow-auto flex-1 bg-white rounded-12px">
        <img
          className="w-full h-full absolute inset-0 opacity-40"
          src={BgImage}
          alt=""
        />
        <div className="relative w-full h-full flex flex-col p-2 pt-0 rounded-12px overflow-y-auto no-scrollbar">
          <div className="flex gap-2 w-full relative py-2">
            {/* Time Column */}
            <div className="w-14 min-w-14">
              {/* Empty cell for header */}
              <div style={{ height: `${HEADER_HEIGHT}px` }}></div>{" "}
              <div style={{ height: `${maxTodoHeight}px` }} className="py-1" />
              {timeSlots.map((time, index) => (
                <div
                  key={index}
                  className={cn(
                    "px-2 text-xs -tracking-wider",
                    index <= currentTimeIndex ? "text-black/50" : "text-black"
                  )}
                  style={{ height: `${CELL_HEIGHT}px` }}>
                  {time}
                </div>
              ))}
            </div>

            {/* Days Columns */}
            <div className="w-full">
              <div className=" flex justify-between gap-2 w-full">
                {days.map((day, dayIndex) => {
                  const todayTODO = getTodaysTodos(Todos, day);

                  return (
                    <div
                      key={dayIndex}
                      className={cn(
                        "flex-1 rounded-xl p-1.5  drop-shadow-calendarShadow shadow-calendarInnerShadow bg-white",
                        isToday(day) && "bg-fb-bPrime-50", // Highlight today,
                        isEventPastDate(day) ? "opacity-50" : "opacity-100",
                        " w-full min-w-[13%] max-w-[13%]"
                      )}>
                      {/* Day Header */}
                      <div
                        className={cn(
                          "px-0 lg:px-2 text-end -tracking-wider flex justify-center flex-col  sticky top-0 bg-white/10 backdrop-blur-sm rounded-md",
                          "z-10"
                        )}
                        style={{ height: `${HEADER_HEIGHT}px` }}>
                        <div className="text-base md:text-lg !leading-4 font-semibold">
                          {format(day, "dd")}
                        </div>
                        <div className="text-xs md:text-sm !leading-4 font-extralight">
                          {format(day, "EEE")}
                        </div>
                      </div>

                      {todayTODO && (
                        <div
                          className="flex flex-col gap-0 py-1"
                          style={{ height: `${maxTodoHeight}px` }}>
                          {todayTODO.todoList.map((tod, ind) => {
                            return (
                              <div
                                className="flex items-center gap-2 bg-white shadow-md rounded-full px-3"
                                style={{ height: TODO_CELL_HEIGHT }}
                                key={ind}
                                title={tod.task}>
                                <p className="line-clamp-1 text-xs">
                                  {tod.task}
                                </p>
                                <Flame
                                  className={cn(
                                    "w-4 min-w-4 ml-auto fill-fb-option-3",
                                    tod.priorityImg ===
                                      ToDoStatusValue.URGENT &&
                                      "fill-fb-option-4",
                                    tod.priorityImg ===
                                      ToDoStatusValue.NO_IMPORTANT &&
                                      "fill-fb-option-3",
                                    tod.priorityImg ===
                                      ToDoStatusValue.COMPLETED &&
                                      "fill-fb-option-7",
                                    tod.status === ToDoStatusValue.COMPLETED &&
                                      "fill-fb-option-7"
                                  )}
                                  strokeWidth={1.3}
                                />
                              </div>
                            );
                          })}
                        </div>
                      )}
                      {/* Day Content with Events */}
                      <div className="relative">
                        {/* Time Slots */}
                        {timeSlots.map((_, index) => (
                          <div
                            key={index}
                            style={{ height: `${CELL_HEIGHT}px` }}></div>
                        ))}

                        {/* Events */}
                        {getEventsForDay(day).map((event) => {
                          const styles = getEventStyle(event, day);

                          // const height = parseFloat(styles.height); // Convert "0px" to 0

                          // if (height <= 0) return null; // Don't render if height is 0 or less

                          return (
                            <div
                              key={event._id}
                              className={cn(
                                "absolute left-0 right-0 rounded-8px text-xs bg-fb-bPrime-hgts",
                                "overflow-hidden flex",
                                isEventEnd(event.end)
                                  ? "opacity-50"
                                  : "opacity-100"
                              )}
                              style={styles}>
                              <div className="bg-fb-bPrime-border w-2 h-full" />
                              <div className="p-1.5 overflow-y-auto no-scrollbar ">
                                <div className="font-medium text-xxs -tracking-wider">
                                  {formatTimeOnly(event.start)} -{" "}
                                  {formatTimeOnly(event.end)}
                                </div>
                                <div className="font-medium text-xxs xl:text-xs -tracking-wider">
                                  {event.title}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {isSameDay(currentDate, currentDate) && (
              <div
                ref={timeIndicatorRef}
                className="absolute left-0 right-0 z-10"
                style={{ top: `${getCurrentTimePosition()}px` }}>
                <div className="absolute -left-1 -top-1.5 w-3 h-3 rounded-full bg-red-500"></div>
                <div className="border-t border-dashed border-red-500 w-full"></div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

const isEventPastDate = (eventD: string | Date) => {
  const eventDate = new Date(eventD).setHours(0, 0, 0, 0); // Normalize to midnight
  const today = new Date().setHours(0, 0, 0, 0);

  return eventDate < today;
};

const isEventEnd = (eventD: string | Date) => {
  const eventDate = new Date(eventD);
  const today = new Date();

  return eventDate < today;
};

// const getEventStyleOld = (event: EventModelT, day: Date) => {
//   const startHour =
//     new Date(event.start).getHours() + new Date(event.start).getMinutes() / 60;
//   const endHour =
//     new Date(event.end).getHours() + new Date(event.end).getMinutes() / 60;

//   // Calculate top position (relative to 00 AM start)
//   const top = startHour * CELL_HEIGHT; // cell height per hour

//   // Calculate height based on duration
//   const height = (endHour - startHour) * CELL_HEIGHT;

//   return {
//     top: `${top}px`,
//     height: `${height}px`,
//   };
// };

const getEventStyle = (event: EventModelT | BizzEventModelT, day: Date) => {
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);

  // Normalize the given day to 00:00:00 to get midnight reference
  const dayStart = new Date(day.setHours(0, 0, 0, 0));

  // Set the start and end time based on the given day
  const startDate = new Date(dayStart);
  startDate.setHours(
    eventStart.getHours(),
    eventStart.getMinutes(),
    eventStart.getSeconds()
  );

  const endDate = new Date(dayStart);
  endDate.setHours(
    eventEnd.getHours(),
    eventEnd.getMinutes(),
    eventEnd.getSeconds()
  );

  let top = 0;
  let height = 0;

  // Case 1: Event is on the same day as the given day
  if (isSameDay(eventStart, eventEnd) && isSameDay(eventStart, day)) {
    const startHour = eventStart.getHours() + eventStart.getMinutes() / 60;
    const endHour = eventEnd.getHours() + eventEnd.getMinutes() / 60;

    top = startHour * CELL_HEIGHT;
    height = (endHour - startHour) * CELL_HEIGHT;
  }
  // Case 2: Event starts on the given day but ends after the day
  else if (isSameDay(eventStart, day)) {
    const startHour = eventStart.getHours() + eventStart.getMinutes() / 60;

    top = startHour * CELL_HEIGHT;
    height = CELL_HEIGHT * 24 - top; // Full day height from start time to the end of the day
  }
  // Case 3: Event ends on the given day but started before that day
  else if (isSameDay(eventEnd, day)) {
    const endHour = eventEnd.getHours() + eventEnd.getMinutes() / 60;

    top = 0; // Start from the beginning of the day
    height = endHour * CELL_HEIGHT; // Height is up to the end time
  }
  // Case 4: Event spans multiple days (intermediate days)
  else if (eventStart < dayStart && eventEnd > dayStart) {
    top = 0;
    height = CELL_HEIGHT * 24; // Full day height for the event
  }

  return {
    top: `${top}px`,
    height: `${height}px`,
  };
};
