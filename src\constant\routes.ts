import {
  faBell,
  faCalendar,
  faHome,
  faListUl,
  faSquarePlus,
  faUser,
  faUserGroup,
} from "@fortawesome/free-solid-svg-icons";

import { NavRouteT } from "@/types";

export const NavRoutes: NavRouteT[] = [
  {
    icon: faHome,
    label: "Home",
    path: "/",
    isIcon: true,
  },
  {
    icon: faListUl,
    label: "To Do",
    path: "/to-do",
    isIcon: true,
  },
  {
    icon: faCalendar,
    label: "Calendar",
    path: "/calendar",
    isIcon: true,
  },
  {
    icon: faSquarePlus,
    label: "Add Events",
    path: "/add-event",
    isIcon: true,
  },
  {
    icon: faUserGroup,
    label: "Following",
    path: "/following",
    isIcon: true,
  },
  {
    icon: faBell,
    label: "Notifications",
    path: "/notifications",
    isIcon: true,
  },
  {
    icon: faUser,
    label: "Profile",
    path: "/profile",
    isIcon: false,
  },
];
