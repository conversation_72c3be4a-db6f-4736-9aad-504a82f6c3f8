import { ChevronLeft, Flag, Users } from "lucide-react";
import { useMemo, useState } from "react";
import { Link, useNavigate, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import EditIcon from "@/assets/svg/edit-icon.svg";
import YearCalendar from "@/components/calendar/year-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import MonthCalendar from "@/components/calendar/month-calendar";
import WeeklyCalendar from "@/components/calendar/weekly-calendar";
import CalendarTabSwitcher, {
  AvailableCalendarTabs,
} from "@/components/calendar/calendar-tab-switch";
import MessageIcon from "@/assets/svg/message-icon.svg";

import { useCalendarDetailsWithEvents } from "@/hook/useCalenderDetailsWithEvents";
import { useGetAccounDetails } from "@/api/account";
import MainLayoutWrapper from "@/layout/MainLayout";
import SmallLayoutWrapper from "@/layout/SmallLayout";
import UpComingEvents from "@/components/calendar/upcoming-events";
import EventsOnDateView from "@/components/common/event/events-on-date";
import SideCalendarView from "@/components/common/side-calendar-view";
import { useAuth } from "@/context/AuthContext";
import { Image } from "@/components/ui/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";

export default function CalendarDetailsScreen() {
  const { id } = useParams();
  console.log(id, "calendar id");

  const { userId } = useAuth();

  const { events, upComingEvents, apiCalendars } = useCalendarDetailsWithEvents(
    {
      calendarId: id,
    }
  );
  const { data: userAccountData = null } = useGetAccounDetails({
    userId: userId,
  });

  const navigate = useNavigate();

  const [activeTab, setActiveTab] = useState<AvailableCalendarTabs>("Week");

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
  };

  const TodoList = useMemo(() => {
    return userAccountData?.accData[0]?.Tasks || [];
  }, [userAccountData]);

  const CalendarData = apiCalendars || null;

  return (
    <div className="flex flex-col w-full h-dvh">
      <div className="justify-between flex items-end">
        <button
          className="pl-8 flex gap-1 items-center"
          onClick={() => navigate(-1)}>
          <ChevronLeft className="size-5 lg:size-6" />
          <p className="text-sm lg:text-base">Back to Calendars</p>
        </button>
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto">
        {/* side bar */}
        <SmallLayoutWrapper className="px-0 pr-3 pt-3 gap-2 overflow-y-auto no-scrollbar">
          {CalendarData && "bizId" in CalendarData ? (
            <div className="bg-fb-bPrime-100 px-2 py-1 rounded-xl drop-shadow-cardDeatilsShadow flex flex-col gap-1">
              <div className="flex flex-col">
                <p className="font-semibold text-sm md:text-base lg:text-2xl leading-5 -tracking-wide">
                  {CalendarData?.businessName}
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-xs md:text-sm lg:text-base font-light italic">
                    {CalendarData?.businessId}
                  </p>
                </div>
              </div>

              <div className="w-full h-48 rounded-xl overflow-hidden relative">
                <Image src={CalendarData?.picture || ""} />
                <div className="absolute w-full h-full bg-black/60 top-0 left-0" />
              </div>
              <div>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <div className="flex justify-between">
                      <AccordionTrigger className="text-sm py-1 font-normal flex gap-1 no-underline hover:no-underline items-center !justify-start">
                        <Users className="size-4 !rotate-0" />{" "}
                        {CalendarData?.followers?.length} followers
                      </AccordionTrigger>
                      <div className="flex items-center gap-1 mb-auto mt-1">
                        <label htmlFor="private" className="text-sm">
                          Private
                        </label>
                        <Switch
                          id="private"
                          size="md"
                          checked={CalendarData?.private}
                        />
                      </div>
                    </div>
                    <AccordionContent className="py-0">
                      <div className="w-full flex flex-col ">
                        <p className="text-sm font-semibold ">
                          People attending this event
                        </p>
                        <div className="flex flex-col gap-1.5 px-4 py-2">
                          {CalendarData?.followers?.map((row, ind) => {
                            return (
                              <div
                                key={ind}
                                className="flex items-center gap-2">
                                {/* <img
                                src={row.picture}
                                alt=""
                                className="h-5 w-5 overflow-hidden rounded-full object-cover"
                              /> */}
                                <Image
                                  src={row.picture || ""}
                                  className="h-5 w-5 overflow-hidden rounded-full"
                                />
                                <p className="text-xs">{row.displayName}</p>
                                <div className="flex gap-2 ml-auto items-center">
                                  <img
                                    src={MessageIcon}
                                    alt=""
                                    className="h-4"
                                  />
                                  <Flag className="size-3.5" />
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="item-3">
                    <AccordionTrigger className="font-semibold py-1 text-sm text-black/75 no-underline hover:no-underline border-b-0">
                      About the Calendar
                    </AccordionTrigger>
                    <AccordionContent>
                      {CalendarData?.description}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </div>
          ) : (
            <div className="bg-fb-neutral-100 p-2 rounded-xl drop-shadow-cardDeatilsShadow flex flex-col gap-1">
              <div
                className="flex flex-col rounded-xl p-3 h-40 justify-end"
                style={{ backgroundColor: CalendarData?.color || "#C0D4F1" }}>
                <p className="font-semibold text-sm md:text-base lg:text-2xl leading-5 -tracking-wide">
                  {CalendarData?.calendarName}
                </p>
              </div>

              <Link to={`/edit-calendar/${id}`}>
                <Button className="w-full h-7 px-4 py-2 text-xs  lg:text-base text-black bg-white drop-shadow-buttonShadow hover:bg-white/80">
                  <img src={EditIcon} alt="" className="size-4 lg:size-5" />
                  Edit
                </Button>
              </Link>
            </div>
          )}
          {activeTab != "Upcoming" && (
            <div className="flex w-full h-full  justify-center items-center">
              <EventsOnDateView />
            </div>
          )}
          {/* calendar view */}
          {activeTab === "Upcoming" && <SideCalendarView />}
        </SmallLayoutWrapper>
        {/* tab views */}
        <MainLayoutWrapper className="bg-white rounded-2xl">
          <CalendarTabSwitcher
            defaultTab={activeTab}
            onTabChange={handleTabChange}
          />

          {activeTab === "Upcoming" && (
            <div className="bg-white w-full h-full flex flex-col p-6 rounded-2xl overflow-y-auto no-scrollbar">
              <UpComingEvents events={upComingEvents} TodoList={TodoList} />
            </div>
          )}
          {activeTab === "Week" && (
            <WeeklyCalendar events={events} Todos={TodoList} />
          )}
          {activeTab === "Month" && <MonthCalendar events={events} />}
          {activeTab === "Year" && <YearCalendar />}
        </MainLayoutWrapper>
      </div>
    </div>
  );
}
