import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export function ConformationPopup({
  isOpen,
  title,
  subTitle,
  onCLose,
}: {
  isOpen: boolean;
  title: string;
  subTitle: string;
  onCLose: () => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onCLose}>
      <DialogContent className="sm:max-w-[500px] min-h-60 !rounded-xl">
        <DialogHeader>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-3 py-4 text-lg lg:text-2xl leading-6">
          <p className="text-center font-semibold">{title}</p>
          <p className="text-center  font-semibold text-fb-neutral-800">
            {subTitle}
          </p>
        </div>
        <DialogFooter className="!justify-center flex  w-full items-center">
          <Button
            className={cn(
              "w-40  rounded-lg bg-fb-success-600 hover:bg-lime-700 drop-shadow-buttonShadow h-9"
            )}
            onClick={onCLose}>
            Ok
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
