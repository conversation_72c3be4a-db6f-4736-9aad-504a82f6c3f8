import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "../endpoints";

import { API } from "@/utils/axios";
import { CalendarModelT } from "@/types";
import { CreatCalendarFormT, StatusCodes } from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { Query_Key } from "@/constant/data";

export const useGetAllCalendar = (payload: { usrId?: string | null }) => {
  return useQuery<CalendarModelT[]>({
    queryKey: [Query_Key.allCalendar],
    queryFn: () => Api_Get_Calendars(payload),
    refetchInterval: 10000, // Auto refresh every 10 seconds
    staleTime: 60000, // Keeps data fresh for 60 seconds before refetching
    enabled: !!payload.usrId, // ⛔ won't run unless usrId is truthy
  });
};

export const useGetCalendarDetails = (payload: { calendarIds?: string[] }) => {
  return useQuery<CalendarModelT[]>({
    queryKey: [Query_Key.calendarDetails, payload.calendarIds],
    queryFn: () => Api_Get_Calendar_Details(payload),
    refetchInterval: 30000, // Auto refresh every 30 seconds
    staleTime: 60000, // Keeps data fresh for 60 seconds before refetching
    enabled: payload.calendarIds && payload.calendarIds.length > 0, // ⛔ won't run unless usrId is truthy
  });
};

export const useCreateCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Calendar,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allCalendar] });
    },
  });
};

export const useUpdateCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Calendar,
    onSuccess(_, { _id }) {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allCalendar] });
      queryClient.invalidateQueries({
        queryKey: [Query_Key.calendarDetails, [_id]],
      });
    },
  });
};

const Api_Get_Calendars = async (payload: {
  usrId?: string | null;
}): Promise<CalendarModelT[]> => {
  const FailureMessage = "Failed to get calendars...";

  try {
    const response = await API.post(API_ENDPOINTS.calendar.getAll, payload);
    const result = handleApiResponse<CalendarModelT[]>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Get_Calendar_Details = async ({
  calendarIds,
}: {
  calendarIds?: string[];
}): Promise<CalendarModelT[]> => {
  const FailureMessage = "Failed to get calendar...";

  try {
    const response = await API.post(
      API_ENDPOINTS.calendar.singleOne,
      calendarIds
    );
    const result = handleApiResponse<CalendarModelT[]>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Create_Calendar = async (
  data: CreatCalendarFormT
): Promise<{ _id: string }> => {
  const FailureMessage = "Failed to creat calendar...";

  try {
    const response = await API.post(API_ENDPOINTS.calendar.create, data);
    const result = handleApiResponse<{ _id: string }>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_Calendar = async (data: CreatCalendarFormT): Promise<null> => {
  const FailureMessage = "Failed to update calendar...";

  try {
    const response = await API.put(API_ENDPOINTS.calendar.update, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
