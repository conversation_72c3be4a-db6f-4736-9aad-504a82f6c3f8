import { useMemo, useState } from "react";
import { X } from "lucide-react";

import { BizzEventModelT, EventModelT } from "@/types";
import { Input } from "@/components/ui/input";
import BgImage from "@/assets/images/bg-image.png";
import SadCalendar from "@/components/common/sad-calendar";
import MoodCalendar from "@/components/common/mood-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import SideCalendarView from "@/components/common/side-calendar-view";
import EventsOnDateView from "@/components/common/event/events-on-date";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import DateGroupEventList from "@/components/common/event/date-wise-group-events";
import MainLayoutWrapper from "@/layout/MainLayout";
import SmallLayoutWrapper from "@/layout/SmallLayout";
import { useAuth } from "@/context/AuthContext";
import AppLoader from "@/components/common/app-loader";

export default function SearchScreen() {
  const [search, setSearch] = useState<string>("");

  const { userId } = useAuth();

  const { events, loading, TodoList } = useCalendarsWithEvents(userId);

  const EventsList = useMemo(() => {
    let ListofData: (EventModelT | BizzEventModelT)[] = [];

    if (events) {
      ListofData = events.filter(
        (eve) =>
          // eve?.businessName
          //   ?.toLocaleLowerCase()
          //   ?.includes(search.toLocaleLowerCase()) ||
          eve.calendarName
            ?.toLocaleLowerCase()
            ?.includes(search.toLocaleLowerCase()) ||
          eve.title?.toLocaleLowerCase()?.includes(search.toLocaleLowerCase())
      );
    }

    return ListofData;
  }, [search, events]);

  if (loading) return <AppLoader />;

  return (
    <div className="flex w-full h-dvh">
      <MainLayoutWrapper>
        {/* search */}
        <div className="flex w-full px-6 my-4">
          <Input
            id="search"
            name="search"
            value={search}
            placeholder="Search"
            onChange={(e) => setSearch(e.target.value)}
            className="bg-fb-neutral-40 border-none placeholder:text-black/70"
          />
        </div>
        {/* no events */}
        <div className="relative overflow-auto flex-1 bg-white rounded-12px">
          <img
            className="w-full h-full absolute inset-0 opacity-40"
            src={BgImage}
            alt=""
          />
          {search ? (
            <div className=" w-full h-full flex flex-col p-6  overflow-y-auto no-scrollbar relative">
              <button className="ml-auto p-1 absolute rounded-full hover:bg-gray-200 transition-all duration-300 top-3 right-7 z-10">
                <X size={18} />
              </button>
              <DateGroupEventList events={EventsList} Todos={TodoList} />
            </div>
          ) : (
            <div className="w-full h-full flex p-6   relative justify-center items-center">
              <SadCalendar text="Search through your Events" />
            </div>
          )}
        </div>
      </MainLayoutWrapper>
      {/* events side data */}
      <SmallLayoutWrapper>
        {/* bussiness details */}

        <BussinessInfo />
        {/* events */}
        {search ? (
          <div className="flex w-full h-full justify-center items-center">
            <MoodCalendar
              text="Click on the event to learn more"
              className="text-base"
            />
          </div>
        ) : (
          <EventsOnDateView />
        )}

        {/* calendar view */}
        <SideCalendarView />
      </SmallLayoutWrapper>
    </div>
  );
}
