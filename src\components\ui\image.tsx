import * as React from "react";
import PlaceholderImage from "@/assets/images/placeholder-calendar.png";
import { cn } from "@/lib/utils"; // assuming 'cn' is a utility function for classNames

// Define the type for props, ensuring 'src' is a string
type ImageProps = React.ComponentProps<"img"> & {
  fallbackSrc?: string; // optional fallback image if the source is invalid
};

const Image = React.forwardRef<HTMLImageElement, ImageProps>(
  (
    { className, src, fallbackSrc = PlaceholderImage, alt = "Image", ...props },
    ref
  ) => {
    // Fallback to dummy image if src is not provided or is invalid
    const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
      e.currentTarget.src = fallbackSrc; // Set to fallback image on error
    };

    return (
      <img
        ref={ref}
        className={cn("w-full h-full object-cover", className)}
        src={src || fallbackSrc} // If no src, fallback to dummy image
        alt={alt}
        onError={handleError} // Handle image loading errors
        {...props}
      />
    );
  }
);

Image.displayName = "Image";

export { Image };
