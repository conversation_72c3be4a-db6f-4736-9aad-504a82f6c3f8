import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAngleLeft, faAngleRight } from "@fortawesome/free-solid-svg-icons";

import { formatEventDate, formatEventMonth } from "@/utils";

export function EventDateChangeComponent({
  date,
  setDate,
}: {
  date: Date;
  setDate: React.Dispatch<React.SetStateAction<Date>>;
}) {
  const formatedDate = formatEventDate(date);

  const handleNextDay = () => {
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setDate(newDate.getDate() + 1);
      return newDate;
    });
  };

  const handlePreviousDay = () => {
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setDate(newDate.getDate() - 1);
      return newDate;
    });
  };

  return (
    <div className="flex flex-col flex-wrap w-full">
      <p className="text-sm lg:text-base font-light">Events Today</p>
      <div className="flex tracking-tight items-center text-center flex-wrap gap-1">
        <button className="mt-1 mr-auto" onClick={handlePreviousDay}>
          <FontAwesomeIcon icon={faAngleLeft} className="size-4" />
        </button>
        <p className="font-medium  text-sm xl:text-lg flex-nowrap">
          {formatedDate.date},{"  "}
        </p>
        <p className="font-light text-xxs xl:text-xs">{formatedDate.day}</p>
        <button className="mt-1 ml-auto" onClick={handleNextDay}>
          <FontAwesomeIcon icon={faAngleRight} className="size-4" />
        </button>
      </div>
    </div>
  );
}

export function EventMonthChangeComponent({
  date,
  setDate,
}: {
  date: Date;
  setDate: React.Dispatch<React.SetStateAction<Date>>;
}) {
  const formatedDate = formatEventMonth(date);

  const handleNextMonth = () => {
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1); // 🔁 Increment month
      return newDate;
    });
  };

  const handlePreviousMonth = () => {
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1); // 🔁 Decrement month
      return newDate;
    });
  };

  return (
    <div className="flex flex-col flex-wrap w-full">
      <p className="text-sm lg:text-base font-light">Events This month</p>
      <div className="flex tracking-tight items-center text-center flex-wrap gap-1">
        <button className="mt-1 mr-auto" onClick={handlePreviousMonth}>
          <FontAwesomeIcon icon={faAngleLeft} className="size-4" />
        </button>
        <p className="font-medium  text-sm xl:text-lg flex-nowrap">
          {formatedDate.date},{"  "}
        </p>
        <button className="mt-1 ml-auto" onClick={handleNextMonth}>
          <FontAwesomeIcon icon={faAngleRight} className="size-4" />
        </button>
      </div>
    </div>
  );
}
