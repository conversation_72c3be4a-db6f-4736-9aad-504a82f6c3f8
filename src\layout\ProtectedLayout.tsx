import { Outlet, Navigate } from "react-router-dom";

import SideBar from "@/containers/SideBar";
import { useAuth } from "@/context/AuthContext";

const ProtectedLayout = () => {
  const { userId } = useAuth();

  if (!userId) {
    return <Navigate to="/login" replace />;
  }

  return (
    <main className="flex w-full h-dvh bg-fb-neutral-50">
      <SideBar />
      <div className="flex-1 h-dvh overflow-hidden relative">
        <Outlet /> {/* This renders the matched child route */}
      </div>
    </main>
  );
};

export default ProtectedLayout;
