import moment from "moment";
import React, { useEffect, useState } from "react";
import {
  Calendar,
  momentLocalizer,
  Components,
  EventPropGetter,
  DayPropGetter,
  DateHeaderProps,
  Navigate,
  ToolbarProps,
} from "react-big-calendar";
import { ChevronLeft, ChevronRight } from "lucide-react";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { BizzEventModelT, EventModelT } from "@/types";
import { Link } from "react-router";
import { format, isThisMonth } from "date-fns";
import { cn } from "@/lib/utils";

const localizer = momentLocalizer(moment);

type MonthViewProps = {
  events: (EventModelT | BizzEventModelT)[] | undefined;
  hideToolbar?: boolean;
};

const MonthSwitcherToolBar: React.FC<
  ToolbarProps<EventModelT | BizzEventModelT, object> & {
    onDateSelect?: (date: Date) => void;
    currentDate?: Date;
  }
> = ({ label, onNavigate, onDateSelect, currentDate = new Date() }) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [pickerYear, setPickerYear] = useState(currentDate.getFullYear());
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);

  useEffect(() => {
    setPickerYear(currentDate.getFullYear());
    setSelectedMonth(currentDate.getMonth());
  }, [currentDate]);

  const navigateToMonth = (type: "PREV" | "NEXT") => {
    onNavigate(type === "PREV" ? Navigate.PREVIOUS : Navigate.NEXT);
  };

  // Handle month selection in picker
  const handleMonthSelect = (monthIndex: number) => {
    setSelectedMonth(monthIndex);
    const newDate = new Date(pickerYear, monthIndex, 1);

    // Force the calendar to navigate to the selected date
    onNavigate(Navigate.DATE, newDate);

    // Also call the callback if provided
    if (onDateSelect) {
      onDateSelect(newDate);
    }

    setShowDatePicker(false);
  };

  // Navigate picker to previous year
  const goToPreviousYear = () => {
    setPickerYear(pickerYear - 1);
  };

  // Navigate picker to next year
  const goToNextYear = () => {
    setPickerYear(pickerYear + 1);
  };

  // Generate months array
  const getMonths = () => {
    return Array.from({ length: 12 }, (_, index) => {
      const date = new Date(pickerYear, index, 1);
      return {
        index,
        name: format(date, "MMM"),
        fullName: format(date, "MMMM"),
        date,
      };
    });
  };

  // Handle keyboard shortcuts
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setShowDatePicker(false);
      setSelectedMonth(null);
    }
  };

  // Open popup and properly initialize state
  const openDatePicker = () => {
    setShowDatePicker(true);
  };

  return (
    <div className="flex items-center mx-auto gap-2 w-fit relative">
      <button
        onClick={() => navigateToMonth("PREV")}
        className="p-0.5 rounded-full hover:bg-gray-100">
        <ChevronLeft className="h-4 w-4" />
      </button>
      <button
        onClick={openDatePicker}
        className="p-0.5 rounded-full hover:bg-gray-100 flex items-center gap-2"
        title="Jump to month">
        <h2 className="text-sm font-semibold">{label}</h2>
      </button>
      <button
        onClick={() => navigateToMonth("NEXT")}
        className="p-0.5 rounded-full hover:bg-gray-100">
        <ChevronRight className="h-4 w-4" />
      </button>

      {/* Month Picker Popup */}
      {showDatePicker && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50">
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-[320px]">
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900">
                  Select Month
                </h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={goToPreviousYear}
                    className="p-1 rounded-full hover:bg-gray-100">
                    <ChevronLeft className="h-4 w-4" />
                  </button>
                  <span className="text-sm font-medium min-w-[80px] text-center">
                    {pickerYear}
                  </span>
                  <button
                    onClick={goToNextYear}
                    className="p-1 rounded-full hover:bg-gray-100">
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Month Grid */}
              <div className="grid grid-cols-3 gap-2">
                {getMonths().map((month) => {
                  const isSelected = selectedMonth === month.index;
                  const isCurrentMonth = isThisMonth(month.date);
                  const isCalendarMonth =
                    currentDate.getFullYear() === pickerYear &&
                    currentDate.getMonth() === month.index;

                  return (
                    <button
                      key={month.index}
                      onClick={() => handleMonthSelect(month.index)}
                      onKeyDown={handleKeyPress}
                      className={cn(
                        "px-3 py-2 text-sm rounded-lg transition-colors",
                        "hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500",
                        "text-gray-700",
                        isCurrentMonth &&
                          !isSelected &&
                          !isCalendarMonth &&
                          "bg-blue-50 text-blue-600 font-medium",
                        isSelected && "bg-blue-500 text-white font-medium"
                      )}>
                      {month.name}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop for closing popup */}
      {showDatePicker && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowDatePicker(false);
            setSelectedMonth(null);
          }}
        />
      )}
    </div>
  );
};

const DateHeader = ({ date }: DateHeaderProps) => {
  const day = moment(date).format("ddd");
  const dateNumber = moment(date).format("DD");

  return (
    <div className="p-2 pt-3">
      <div className="text-base md:text-lg !leading-4 font-semibold">
        {dateNumber}
      </div>
      <div className="text-xs md:text-sm !leading-4 font-extralight">{day}</div>
    </div>
  );
};

const CustomEvent: React.FC<{ event: EventModelT | BizzEventModelT }> = ({
  event,
}) => {
  return (
    <Link to={`/event/${event._id}`}>
      <div className="overflow-hidden rounded-xl bg-fb-bPrime-50 text-black text-xs flex">
        <div className="bg-fb-bPrime-border  w-2" />
        <div className="font-semibold px-2 py-0.5">{event.title}</div>
      </div>
    </Link>
  );
};

const components: Components<EventModelT | BizzEventModelT> = {
  month: {
    header: () => undefined,
    dateHeader: DateHeader,
    event: CustomEvent,
  },
  eventWrapper: (({ children }: { children: React.ReactNode }) => (
    <div className="px-1">{children}</div>
  )) as React.FC,
  toolbar: MonthSwitcherToolBar,
};

const eventPropGetter: EventPropGetter<unknown> = () => ({
  style: {
    borderRadius: "14px",
    padding: 0,
    width: "calc(100% - 4px)",
    backgroundColor: "#DDE0E7",
    color: "#000",
    fontWeight: 500,
    fontSize: "12px",
    border: "none",
  },
});

const dayPropGetter: DayPropGetter = (date) => ({
  style: {
    backgroundColor: moment(date).isSame(new Date(), "day")
      ? "#DDE0E7"
      : "white",
    borderRadius: "12px",
    border: "none",
    margin: "4px 2px",
    boxShadow:
      "inset -2px -2px 8px rgba(0, 0, 0, 0.07), 2px 2px 4px rgba(0, 0, 0, 0.07)",
    padding: "2px",
  },
});

const MonthCalendar = ({ events, hideToolbar = false }: MonthViewProps) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const handleDateSelect = (date: Date) => {
    setCurrentDate(date);
  };

  // Handle calendar navigation (arrow buttons, etc.)
  const handleNavigate = (date: Date) => {
    console.log("Calendar navigating to:", date);
    console.log("New month:", date.getMonth(), "Year:", date.getFullYear());
    setCurrentDate(date);
  };

  const modifiedComponents: Components<EventModelT | BizzEventModelT> = {
    ...components,
    toolbar: (props: ToolbarProps<EventModelT | BizzEventModelT, object>) => (
      <MonthSwitcherToolBar
        {...props}
        onDateSelect={handleDateSelect}
        currentDate={currentDate}
      />
    ),
  };

  return (
    <Calendar
      className="border-none shadow-none"
      localizer={localizer}
      date={currentDate}
      onNavigate={handleNavigate}
      views={["month"]}
      components={modifiedComponents}
      events={events}
      eventPropGetter={eventPropGetter}
      dayPropGetter={dayPropGetter}
      popup={true}
      toolbar={!hideToolbar}
      slotPropGetter={() => ({
        style: {
          minHeight: "40px",
        },
      })}
    />
  );
};

export default MonthCalendar;
