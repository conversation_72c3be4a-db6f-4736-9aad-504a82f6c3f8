import axios, { AxiosError } from "axios";

// import { BASE_URL } from "@/envs";

export const API = axios.create({
  // baseURL: BASE_URL,
  // baseURL: "/",
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

API.interceptors.request.use((config) => {
  return config;
});

API.interceptors.response.use(
  (response) => response,
  (error) => {
    const axiosError = error as AxiosError;
    return Promise.reject(axiosError);
  }
);
