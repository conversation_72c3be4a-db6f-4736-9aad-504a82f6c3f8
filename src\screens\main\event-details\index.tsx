import { X } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { formateEventFullDate } from "@/utils";
import BgImage from "@/assets/images/bg-image.png";
import SadCalendar from "@/components/common/sad-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import SideCalendarView from "@/components/common/side-calendar-view";

import EventDetailsView from "./components/event-details";
import EventsOnDateView from "@/components/common/event/events-on-date";
import { useGetEventDetails } from "@/api/event";
import AppLoader from "@/components/common/app-loader";

export default function EventDetailsScreen() {
  const { id } = useParams();

  console.log(id, "event id---");
  const navigation = useNavigate();

  const {
    data = null,
    isLoading,
    error,
  } = useGetEventDetails({ eventId: id as string });

  if (isLoading) return <AppLoader />;
  const { date } = formateEventFullDate(data?.start || new Date());

  return (
    <div className="flex w-full h-dvh">
      <div className="flex flex-col gap-3 py-3 w-[calc(100%-250px)] h-full">
        {!error && !isLoading ? (
          <div className="bg-white w-full h-full flex flex-col  p-4 rounded-2xl overflow-y-auto no-scrollbar">
            <div className="flex justify-between items-center">
              <p className="text-base font-light">{date}</p>
              <button
                className="ml-auto p-1 rounded-full hover:bg-gray-200 transition-all duration-300"
                onClick={() => {
                  navigation(-1);
                }}>
                <X size={18} />
              </button>
            </div>
            <EventDetailsView data={data} />
          </div>
        ) : (
          <div className="bg-white w-full h-full flex p-6  rounded-2xl justify-center items-center relative">
            <img
              className="w-full h-full absolute inset-0 opacity-40"
              src={BgImage}
              alt=""
            />
            <button
              className="ml-auto absolute p-1 rounded-full hover:bg-gray-200 transition-all duration-300 top-6 right-8 z-10"
              onClick={() => {
                navigation(-1);
              }}>
              <X size={18} />
            </button>
            <SadCalendar text={error?.message || "Event not found"} />
          </div>
        )}
      </div>
      {/* events side data */}
      <div className="w-64 lg:w-80 px-4 py-3 flex flex-col gap-3  overflow-y-auto">
        {/* bussiness details */}

        <BussinessInfo />

        <EventsOnDateView />

        {/* calendar view */}
        <SideCalendarView />
      </div>
    </div>
  );
}
