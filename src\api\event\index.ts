import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "../endpoints";

import { API } from "@/utils/axios";
import { EventModelT } from "@/types";
import { GetAllEventsResT, StatusCodes } from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { Query_Key } from "@/constant/data";
import { Api_Upload_Image } from "../helper";

export const useGetAllEvents = (payload: { userId?: string | null }) => {
  return useQuery<GetAllEventsResT>({
    queryKey: [Query_Key.allEventsCalendar],
    queryFn: () => Api_Get_Events(payload.userId),
    refetchInterval: 10000, // Auto refresh every 10 seconds
    staleTime: 60000, // Keeps data fresh for 60 seconds before refetching
    enabled: !!payload.userId, // ⛔ won't run unless userId is truthy
  });
};

export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Event,
    onSuccess() {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.allEventsCalendar],
      });
    },
  });
};

export const useGetEventDetails = (payload: { eventId?: string }) => {
  return useQuery<EventModelT>({
    queryKey: [Query_Key.eventDetails, payload.eventId],
    queryFn: () => Api_Get_Event_Details(payload.eventId),
    refetchInterval: 10000, // Auto refresh every 10 seconds
    staleTime: 60000, // Keeps data fresh for 60 seconds before refetching
    enabled: !!payload.eventId, // ⛔ won't run unless eventId is truthy
  });
};

export const useUpdateEvent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Event,
    onSuccess(_, { _id }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.allEventsCalendar],
      });
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, _id],
      });
    },
  });
};

export const useGetEventById = (eventId: string) => {
  return useQuery<EventModelT>({
    queryKey: [Query_Key.eventDetails, eventId],
    queryFn: () => Api_Get_Event_Details(eventId),
    enabled: !!eventId,
  });
};

const Api_Get_Events = async (
  usrId?: string | null
): Promise<GetAllEventsResT> => {
  const FailureMessage = "Failed to get events...";

  try {
    const response = await API.post(API_ENDPOINTS.event.getUserEvents, {
      usrId,
    });
    const result = handleApiResponse<GetAllEventsResT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Create_Event = async (
  data: EventModelT
): Promise<{ _id: string }> => {
  const FailureMessage = "Failed to creat event...";

  const { profileImage, ...rest } = data;

  try {
    const imageUri = await Api_Upload_Image(profileImage);

    rest.picture = imageUri;

    const response = await API.post(API_ENDPOINTS.event.create, rest);

    const result = handleApiResponse<{ _id: string }>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Get_Event_Details = async (eventId: string | undefined) => {
  const FailureMessage = "Failed to get event details...";

  const payload = {
    _id: eventId,
  };

  try {
    const response = await API.post(API_ENDPOINTS.event.details, payload);
    const result = handleApiResponse<EventModelT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );
    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_Event = async (data: Partial<EventModelT>): Promise<null> => {
  const FailureMessage = "Failed to update event...";

  const { profileImage, ...rest } = data;

  try {
    let imageUri = rest.picture;
    if (profileImage) {
      console.log("----update profile image---", profileImage);
      imageUri = await Api_Upload_Image(profileImage);
      rest.picture = imageUri;
    }

    const response = await API.put(API_ENDPOINTS.event.update, rest);

    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
