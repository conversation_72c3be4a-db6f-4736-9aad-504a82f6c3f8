/** @type {import('tailwindcss').Config} */

import tailwindcssAnimate from "tailwindcss-animate";

export default {
  darkMode: ["class"],
  content: ["./src/**/*.{html,js,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        sm: "calc(var(--radius) - 4px)",
        md: "calc(var(--radius) - 2px)",
        "8px": "var(--radius)",
        "10px": "calc(var(--radius) + 2px)",
        "12px": "calc(var(--radius) + 4px)",
        lg: "14px",
        xl: "16px",
        "2xl": "24px",
        "3xl": "56px",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          1: "hsl(var(--chart-1))",
          2: "hsl(var(--chart-2))",
          3: "hsl(var(--chart-3))",
          4: "hsl(var(--chart-4))",
          5: "hsl(var(--chart-5))",
        },
        fb: {
          yellow: {
            DEFAULT: "#FCE803",
            200: "#FFE946",
          },
          red: {
            200: "#D72837",
            DEFAULT: "#EE2A0F",
          },
          cadelBlue: {
            500: "#D9D9D9",
            600: "#8394AE",
            DEFAULT: "#ACB7CA",
          },
          neutral: {
            40: "#CED1D8",
            50: "#F8F8F9",
            100: "#ECEDF0",
            200: "#E1E3E7",
            300: "#CED1D8",
            400: "#C0C3CB",
            500: "#9EA3B0",
            600: "#83899A",
            700: "#697082",
            800: "#484D59",
            900: "#060607",
          },
          pink: "#FF3980",
          purple: "#9E4EA5",
          success: {
            600: "#518524",
          },
          warn: {
            100: "#FFC2C2",
            400: "#FF3C3C",
            500: "#D90000",
            600: "#AE0000",
          },
          bPrime: {
            50: "#DDE0E7",
            100: "#ACB7CA",
            400: "#526179",
            500: "#404B5E",
            600: "#333C4B",
            hgts: "#98A4B9",
            border: "#465164",
            darkText: "#848a9a",
            placeholder: "#B2B2B2",
            smile: "#292D32",
          },
          uPrime: {
            100: "#CFF4FC",
            300: "#00C2FF",
            400: "#3FD1F1",
            500: "#0FC6EE",
          },
          option: { 3: "#F1DDC0", 4: "#F1C0C0", 7: "#C0D4F1" },
        },
      },
      boxShadow: {
        cardInnerShadow: "inset -4px -4px 4px rgba(0, 0, 0, 0.10)",
        calendarInnerShadow: "inset -2px -2px 8px rgba(0, 0, 0, 0.07)",
        calenViewShadow: "inset -2.22px -2.22px 8.89px rgba(0, 0, 0, 0.07)",
        boxViewShadow: "inset 0px 4px 10px rgba(0, 0, 0, 0.12)",
      },
      dropShadow: {
        cardOutShadow: "4px 4px 7.6px rgba(0, 0, 0, 0.10)",
        inputShadow: "0px 4px 10px rgba(0, 0, 0, 0.12)",
        buttonShadow: "1px 2px 4px rgba(0, 0, 0, 0.15)",
        cardShadow: "2px 2px 9px  rgba(0, 0, 0, 0.10)",
        calendarShadow: "2px 2px 4px rgba(0, 0, 0, 0.07)",
        calenViewShadow: "2.22px 2.22px 4.44px rgba(0, 0, 0, 0.07)",
        cardDeatilsShadow: "0px 2px 12px rgba(0, 0, 0, 0.15)",
      },
      fontSize: {
        xxs: "10px",
        xxxs: "8px",
      },
      width: {
        97: "400px",
        98: "436px",
      },
      height: { 50: "200px" },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [tailwindcssAnimate],
};
