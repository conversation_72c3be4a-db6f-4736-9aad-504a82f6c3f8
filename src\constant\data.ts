import { AddEventFieldValue, ProfileFieldValue } from "@/enums";

export const MonthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

export const WeekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

export const AddEventFields = [
  { name: "Location", value: AddEventFieldValue.LOCATION },
  { name: "<PERSON>minder", value: AddEventFieldValue.REMINDER },
  { name: "Repeat", value: AddEventFieldValue.REPEAT },
  { name: "Description", value: AddEventFieldValue.DESCRIPTION },
  { name: "Attachments", value: AddEventFieldValue.ATTACHMENTS },
  { name: "Responses", value: AddEventFieldValue.RESPONSES },
];

export const ProfileFields = [
  { name: "Details", value: ProfileFieldValue.DETAILS, toTrasfer: false },
  { name: "Setting<PERSON>", value: ProfileFieldValue.SETTINGS, toTrasfer: false },
  {
    name: "Terms and Condition",
    value: ProfileFieldValue.TERMS_AND_CONDITION,
    toTrasfer: true,
    to: "#",
  },
  {
    name: "Privacy Policy",
    value: ProfileFieldValue.PRIVACT_POLICY,
    toTrasfer: true,
    to: "#",
  },
  {
    name: "Sync All Devices",
    value: ProfileFieldValue.SyncAllDevices,
    toTrasfer: false,
  },
];

export const ReviewFileds = [
  "Privacy Concerns",
  "Unnecessary or Unused",
  "Security Concerns",
  "Not Satisfied with the Product",
  "Switching Platforms",
  "Limited Storage Space",
  "Ethics and Ideological Reasons",
  "Consumes too Much Battery",
  "Subscription Cost Issues",
  "Performance Issues",
];

export const COLOR_LIST = [
  "#C0F1DF",
  "#DBF1C0",
  "#F1DDC0",
  "#F1C0C0",
  "#F1C0EC",
  "#CAC0F1",
  "#C0D4F1",
];

export const RepeatDrop = [
  { value: "Never", label: "Never" },
  { value: "Daily", label: "Daily" },
  { value: "Weekly", label: "Weekly" },
  { value: "Monthly", label: "Monthly" },
];

export const RepeatDays = [
  { value: "Sunday", label: "Subday" },
  { value: "Monday", label: "Monday" },
  { value: "Tuesday", label: "Tuesday" },
  { value: "Wednesday", label: "Wednesday" },
  { value: "Thursday", label: "Thursday" },
  { value: "Friday", label: "Friday" },
  { value: "Saterday", label: "Saterday" },
];

export const RepeatDates = Array.from({ length: 365 }).map((_, i) => ({
  value: (i + 1).toString(),
  label: i + 1,
}));

export const Query_Key = {
  allCalendar: "allCalendar",
  calendarDetails: "calendarDetails",
  accountDetails: "accountDetails",
  allEventsCalendar: "allEventsCalendar",
  allNotifications: "allNotifications",
  allBizzCalendar: "allBizzCalendar",
  eventDetails: "eventDetails",
};
