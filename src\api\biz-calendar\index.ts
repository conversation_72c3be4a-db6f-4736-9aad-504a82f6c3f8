import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "../endpoints";

import { API } from "@/utils/axios";
import { BizzCalendarModelT } from "@/types";
import { FollowBizzCalendarFormT, StatusCodes } from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { Query_Key } from "@/constant/data";

export const useGetAllBizzCalendar = () => {
  return useQuery<BizzCalendarModelT[]>({
    queryKey: [Query_Key.allBizzCalendar],
    queryFn: () => Api_Get_Business_Calendars(),
  });
};

export const useFollowBizzCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Follow_Bizz_Calendar,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useUnfollowBizzCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Unfollow_Bizz_Calendar,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

const Api_Get_Business_Calendars = async (): Promise<BizzCalendarModelT[]> => {
  const FailureMessage = "Failed to get bizz calendars...";

  const payload: string[] = [];

  try {
    const response = await API.post(API_ENDPOINTS.bizCalendar.getAll, payload);
    const result = handleApiResponse<BizzCalendarModelT[]>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Follow_Bizz_Calendar = async (
  data: FollowBizzCalendarFormT
): Promise<null> => {
  const FailureMessage = "Failed to follow bizz calendar...";

  try {
    const response = await API.post(API_ENDPOINTS.bizCalendar.follow, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Unfollow_Bizz_Calendar = async (
  data: FollowBizzCalendarFormT
): Promise<null> => {
  const FailureMessage = "Failed to unfollow bizz calendar...";

  try {
    const response = await API.post(API_ENDPOINTS.bizCalendar.unfollow, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
