import React from "react";

export type AvailableCalendarTabs = "Upcoming" | "Week" | "Month" | "Year";

type HorizontalTabSwitcherProps = {
  defaultTab: AvailableCalendarTabs;
  onTabChange?: (tab: AvailableCalendarTabs) => void;
};

const CalendarTabSwitch = ({
  defaultTab,
  onTabChange,
}: HorizontalTabSwitcherProps) => {
  const [activeTab, setActiveTab] = React.useState(defaultTab);
  const tabs: AvailableCalendarTabs[] = ["Upcoming", "Week", "Month", "Year"];

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
    onTabChange?.(tab);
  };

  return (
    <div className="flex w-full justify-center items-center px-5 h-4">
      {tabs.map((tab, tabIndex) => (
        <React.Fragment key={tabIndex}>
          <button
            className={`px-4 py-2 text-sm lg:text-base ease-out duration-200 text-[#848a9a] ${
              activeTab === tab
                ? "font-bold underline text-black"
                : "hover:underline"
            }`}
            onClick={() => handleTabChange(tab)}>
            {tab}
          </button>
          {tabIndex !== tabs.length - 1 && (
            <span className="inline-block bg-[#848a9a] w-full h-px" />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default CalendarTabSwitch;
