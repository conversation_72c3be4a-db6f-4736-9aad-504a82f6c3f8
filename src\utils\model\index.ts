import { BizzEventModelT, EventModelT } from "@/types";
import { getZoneFromTzCode } from "..";

export const mapRawBizzEventsToModel = (
  rawEvents: any[]
): BizzEventModelT[] => {
  return rawEvents.map((e) => ({
    _id: e._id,
    businessName: e.businessName || "",
    calendarName: e.calendarName || "",
    calId: e.calId,
    logo: e.logo || "",
    title: e.title || "",
    allDayEvent: !!e.allDayEvent,
    start: e.start,
    end: e.end,
    repeat: !!e.repeat,
    frequency: e.frequency || "Never",
    repeatEvery: e.repeatEvery ?? 1,
    days: e.days || [],
    repeatEndDate: e.repeatEndDate || "",
    location: e.location || "",
    link: e.link || "",
    picture: e.picture || "",
    youtube: e.youtube || "",
    restrict: e.restrict || [],
    zone: getZoneFromTzCode(e.zone?.tzCode || "Asia/Kolkata"),
    attach: e.attach || "",
    description: e.description || "",
    reminderSelected: e.reminderSelected || "",
    reminder: e.reminder || "",
    personalReminder: e.personalReminder || "",
    notifyBefore: e.notifyBefore || "",
    event: !!e.event,
    note: !!e.note,
    color: e.color || "#98A4B9",
    userType: e.userType || "businessUser",
    textColor: e.textColor || "#000000",
    deleted: !!e.deleted,
    blocked: !!e.blocked,
    createdAt: e.createdAt || new Date().toISOString(),
    Impression: e.Impression || [],
    Rsvps: e.Rsvps || [],
    Comments: e.Comments || [],
    Reply: e.Reply || [],
    usrId: e.usrId,
    Agenda: e.Agenda || [],
    restrictRSVP: e.restrictRSVP || false,
    allowComments: e.allowComments || false,
    allowLikes: e.allowLikes || false,
  }));
};

export const mapRawUserEventsToModel = (rawEvents: any[]): EventModelT[] => {
  return rawEvents.map((e) => ({
    _id: e._id,
    businessName: e.businessName || "",
    calendarName: e.calendarName || "",
    calId: e.calId,
    logo: e.logo || "",
    title: e.title || "",
    allDayEvent: !!e.allDayEvent,
    start: e.start,
    end: e.end,
    repeat: !!e.repeat,
    frequency: e.frequency || "Never",
    repeatEvery: e.repeatEvery ?? 1,
    days: e.days || [],
    repeatEndDate: e.repeatEndDate || "",
    location: e.location || "",
    link: e.link || "",
    picture: e.picture || "",
    youtube: e.youtube || "",
    restrict: e.restrict || [],
    zone: getZoneFromTzCode(e.zone?.tzCode || "Asia/Kolkata"),
    attach: e.attach || "",
    description: e.description || "",
    reminderSelected: e.reminderSelected || "",
    reminder: e.reminder || "",
    personalReminder: e.personalReminder || "",
    notifyBefore: e.notifyBefore || "",
    event: !!e.event,
    note: !!e.note,
    color: e.color || "#98A4B9",
    userType: e.userType || "businessUser",
    textColor: e.textColor || "#000000",
    deleted: !!e.deleted,
    blocked: !!e.blocked,
    createdAt: e.createdAt || new Date().toISOString(),
    Impression: e.Impression || [],
    Rsvps: e.Rsvps || [],
    Comments: e.Comments || [],
    Reply: e.Reply || [],
    usrId: e.usrId,
    Agenda: e.Agenda || [],
    restrictRSVP: e.restrictRSVP || false,
    allowComments: e.allowComments || false,
    allowLikes: e.allowLikes || false,
    profileImage: e.profileImage || null,
  }));
};
