import React from "react";

import { cn } from "@/lib/utils";
import { MonthNames, WeekDays } from "@/constant/data";

interface CalendarProps {
  year: number;
  month: number;
  className?: string;
  weekDayClassName?: string;
  isSmallView?: boolean;
}

const MonthCalendarDateView: React.FC<CalendarProps> = ({
  year,
  month,
  className,
  weekDayClassName,
  isSmallView = false,
}: CalendarProps) => {
  const todayDate = new Date().getDate();

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = getFirstDayOfMonth(year, month);
  const firstDayAdjusted = firstDay === 0 ? 6 : firstDay - 1; // Adjust for Monday start

  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const weeks: (number | null)[][] = [];
  let currentWeek: (number | null)[] = Array(firstDayAdjusted).fill(null);

  days.forEach((day) => {
    currentWeek.push(day);
    if (currentWeek.length === 7) {
      weeks.push(currentWeek);
      currentWeek = [];
    }
  });

  if (currentWeek.length > 0) {
    weeks.push([...currentWeek, ...Array(7 - currentWeek.length).fill(null)]);
  }

  return (
    <div className={cn("min-h-fit", className)} key={year}>
      <h2
        className={cn(
          " font-light text-center",
          isSmallView ? "text-sm lg:text-base" : "text-sm lg:text-base"
        )}>
        {MonthNames[month]} {!isSmallView && year}
      </h2>

      <div className="grid grid-cols-7 gap-1 text-black/50 border-b border-black/50">
        {WeekDays.map((day) => (
          <div
            key={day}
            className={cn("font-medium text-center -tracking-wider")}>
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 space-x-0  space-y-0">
        {weeks.flat().map((day, index) => {
          const isToday =
            day === todayDate &&
            month === new Date().getMonth() &&
            year === new Date().getFullYear();
          return (
            <div
              key={index}
              className={cn(
                " flex items-center justify-center rounded-full font-medium transition-colors leading-6",
                day
                  ? "hover:bg-gray-200 cursor-pointer text-gray-900"
                  : "text-transparent",
                isToday && "bg-fb-neutral-300",
                weekDayClassName
              )}>
              {day || ""}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MonthCalendarDateView;
