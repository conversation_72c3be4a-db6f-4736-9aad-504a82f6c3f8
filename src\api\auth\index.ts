import { useMutation } from "@tanstack/react-query";

import {
  loginWithApple,
  loginWithEmail,
  loginWithFacebook,
  loginWithGoogle,
  signUpWithEmail,
} from "@/firebase/authService";

export const useEmailSignUp = () => {
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      signUpWithEmail(email, password),
  });
};

export const useEmailLogin = () => {
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      loginWithEmail(email, password),
  });
};

export const useGoogleLogin = () => {
  return useMutation({
    mutationFn: loginWithGoogle,
  });
};

export const useFacebookLogin = () => {
  return useMutation({
    mutationFn: loginWithFacebook,
  });
};
export const useAppleLogin = () => {
  return useMutation({
    mutationFn: loginWithApple,
  });
};
