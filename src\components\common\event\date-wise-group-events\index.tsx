import { isToday } from "date-fns";
import { Link } from "react-router";

import { Button } from "@/components/ui/button";
import { SquarePlus } from "@/assets/svg/icons";
import { BizzEventModelT, EventModelT, TodoListModelT } from "@/types";
import { formatEventDate } from "@/utils";
import NoEventNote from "@/assets/images/notes-gray-nil.png";

import {
  BizzEventMiniCardView,
  EventListCardView,
  TodoCardView,
} from "../card-view";

type GroupedModelT = {
  date: string;
  todos: TodoListModelT[];
  events: (EventModelT | BizzEventModelT)[];
};

function parseDateString(dateStr: string): Date {
  const [day, month, year] = dateStr.split("/");
  return new Date(`${year}-${month}-${day}`);
}

const groupDataByDate = (
  events: (EventModelT | BizzEventModelT)[],
  todos: TodoListModelT[]
): GroupedModelT[] => {
  const groupedMap = new Map<
    string,
    { todos: TodoListModelT[]; events: (EventModelT | BizzEventModelT)[] }
  >();

  // Group todos
  for (const todo of todos) {
    const dateKey = parseDateString(todo.date).toDateString();

    if (!groupedMap.has(dateKey)) {
      groupedMap.set(dateKey, { todos: [], events: [] });
    }

    groupedMap.get(dateKey)!.todos.push(todo);
  }

  // Group events
  for (const event of events) {
    const dateKey = new Date(event.start).toDateString();
    if (!groupedMap.has(dateKey)) {
      groupedMap.set(dateKey, { todos: [], events: [] });
    }
    groupedMap.get(dateKey)!.events.push(event);
  }

  // Ensure today is included even if empty
  const todayKey = new Date().toDateString();
  if (!groupedMap.has(todayKey)) {
    groupedMap.set(todayKey, { todos: [], events: [] });
  }

  // Convert Map to array and sort by date
  const groupedArray = Array.from(groupedMap.entries())
    .map(([date, { todos, events }]) => ({
      date,
      todos,
      events,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return groupedArray;
};

export default function DateGroupEventList({
  events,
  Todos,
}: {
  events: (EventModelT | BizzEventModelT)[];
  Todos: TodoListModelT[];
}) {
  // const groupedEvents = groupEventsByDate(events);
  const combileTodoAndEvent = groupDataByDate(events, Todos);

  return (
    <div className="flex flex-col gap-3 ">
      {combileTodoAndEvent.map((row, index) => {
        const rawDate = new Date(row.date);
        const isCurrentDay = isToday(rawDate);
        const date = formatEventDate(row.date);

        return (
          <div key={index} className="flex flex-col gap-2 mb-1">
            <div className="flex gap-1 items-center justify-between border-b border-fb-bPrime-hgts px-2">
              <div className="flex gap-1 items-end">
                <p className="font-semibold text-base">{date.date},</p>
                <p className="font-light text-xs">({date.day})</p>
              </div>
              {isCurrentDay && <p className="font-semibold text-sm">Today</p>}
            </div>
            {/* {row.data.map((item, index) => {
              if ("start" in item) {
                // It's an EventModelT
                return <EventListCardView {...item} key={index} />;
              } else {
                // It's a TodoListModelT
                return <TodoCardView {...item} key={index} />;
              }
            })} */}
            {row.todos.map((item, index) => {
              return <TodoCardView {...item} key={index} />;
            })}
            {row.events.length === 0 && isCurrentDay ? (
              <NoEventsForToday />
            ) : (
              row.events.map((item, index) => {
                if ("businessName" in item) {
                  return <BizzEventMiniCardView {...item} key={index} />;
                } else {
                  return <EventListCardView {...item} key={index} />;
                }
              })
            )}
          </div>
        );
      })}
    </div>
  );
}

const NoEventsForToday = () => {
  return (
    <div className="flex flex-col gap-1 justify-center items-center">
      <div className="w-32 lg:w-40 lg:h-40 mx-auto">
        <img
          src={NoEventNote}
          alt=""
          className="w-full h-full object-contain"
        />
      </div>
      <Link to={"/add-event"}>
        <Button
          size={"sm"}
          className="bg-fb-bPrime-600 text-xs py-1 text-white hover:bg-fb-bPrime-500 rounded-full px-4 h-7 drop-shadow-buttonShadow">
          <SquarePlus className="!size-4" />
          Add event Today
        </Button>
      </Link>
    </div>
  );
};
