import { Toaster } from "sonner";
import { BrowserRouter, Route, Routes } from "react-router";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import HomeScreen from "@/screens/main/home";
import AuthProvider from "@/context/AuthContext";
import SearchScreen from "@/screens/main/search";
import LoginScreen from "@/screens/auth/login-in";
import SignUpScreen from "@/screens/auth/singn-up";
import UserProfileScreen from "@/screens/main/profile";
import ProtectedLayout from "@/layout/ProtectedLayout";
import ToDoListScreen from "@/screens/main/to-do-list";
import FollowingScreen from "./screens/main/followings";
import CalendarScreen from "@/screens/main/calendar/main";
import EventDetailsScreen from "@/screens/main/event-details";
import NotificationsScreen from "@/screens/main/notifications";
import AddEventWithEnterScreen from "@/screens/main/add-events";
import SignUpDetailsScreen from "@/screens/auth/sign-up-details";
import AddCalendarScreen from "@/screens/main/calendar/add-calendar";
import FollowingDetailsScreen from "./screens/main/following-details";
import PageUnderConstruction from "@/components/PageUnderConstruction";
import CalendarDetailsScreen from "@/screens/main/calendar/calendar-details";
import EditEventScreen from "./screens/main/edit-event";
import EditCalendarScreen from "./screens/main/edit-calendar";

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Toaster richColors />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Public Route */}
            <Route path="/login" element={<LoginScreen />} />
            <Route path="/signup" element={<SignUpScreen />} />
            <Route path="/signup-details" element={<SignUpDetailsScreen />} />

            {/* Protected Routes (With Sidebar) */}
            <Route element={<ProtectedLayout />}>
              <Route path="/" element={<HomeScreen />} />
              {/* <Route path="/:id" element={<EventDetailsScreen />} />
            <Route path="/add-events" element={<AddEventsScreen />} />
            // <Route path="/event" element={<HomeScreen />} /> */}
              <Route path="/event/:id" element={<EventDetailsScreen />} />
              <Route path="/add-event" element={<AddEventWithEnterScreen />} />
              <Route
                path="/edit-event/:eventId"
                element={<EditEventScreen />}
              />
              <Route path="/search" element={<SearchScreen />} />
              <Route path="/calendar" element={<CalendarScreen />} />
              <Route path="/calendar/:id" element={<CalendarDetailsScreen />} />
              <Route
                path="/calendar/add-calendar"
                element={<AddCalendarScreen />}
              />
              <Route
                path="/edit-calendar/:calendarId"
                element={<EditCalendarScreen />}
              />
              <Route path="/notifications" element={<NotificationsScreen />} />
              <Route path="/to-do" element={<ToDoListScreen />} />
              <Route path="/following" element={<FollowingScreen />} />
              <Route
                path="/following/:id"
                element={<FollowingDetailsScreen />}
              />
              <Route path="/profile" element={<UserProfileScreen />} />
              <Route path="*" element={<PageUnderConstruction />} />
            </Route>
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;
