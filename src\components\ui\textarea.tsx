import * as React from "react";

import { cn } from "@/lib/utils";

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        "flex min-h-[80px] w-full drop-shadow-inputShadow rounded-xl border border-black bg-fb-neutral-50 px-3 py-2 text-fb-neutral-900  disabled:cursor-not-allowed disabled:opacity-50",
        "text-xs lg:text-sm",
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = "Textarea";

export { Textarea };
