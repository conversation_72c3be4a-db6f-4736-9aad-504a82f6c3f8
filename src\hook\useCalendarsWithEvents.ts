import { useMemo } from "react";

import { BizzEventModelT, EventModelT } from "@/types";
import { useGetAllEvents } from "@/api/event";
import { useGetAllCalendar } from "@/api/calendar";
import {
  mapRawBizzEventsToModel,
  mapRawUserEventsToModel,
} from "@/utils/model";
import {
  convertEventTimesToLocal,
  filterUpcomingEvents,
  filterUpcomingTodos,
} from "@/utils";
import { useGetAccounDetails } from "@/api/account";

export const useCalendarsWithEvents = (usrId?: string | null) => {
  const {
    data: calendars = null,
    isLoading: loadingCalendars,
    ...calendarQuery
  } = useGetAllCalendar({ usrId });

  const { data: userAccountData = null } = useGetAccounDetails({
    userId: usrId,
  });

  const { TodoList, upComingTodoList } = useMemo(() => {
    const TodoList = userAccountData?.accData[0]?.Tasks || [];
    const upComingTodoList = filterUpcomingTodos(TodoList);
    return { TodoList, upComingTodoList };
  }, [userAccountData]);

  const {
    data: events,
    isLoading: loadingEvents,
    ...eventsQuery
  } = useGetAllEvents({ userId: usrId });

  const { upComingEvents, LocalTimeZoneEvents } = useMemo(() => {
    let upComingEvents: (EventModelT | BizzEventModelT)[] = [];
    let LocalTimeZoneEvents: (EventModelT | BizzEventModelT)[] = [];

    if (events) {
      const { businessEvents, endUserEvents } = events;

      const formatedEvent = convertEventTimesToLocal(
        mapRawUserEventsToModel(endUserEvents)
      );

      const formatedBusinessEvent = convertEventTimesToLocal(
        mapRawBizzEventsToModel(businessEvents)
      );

      const upComingUserEvents = filterUpcomingEvents(formatedEvent);
      const upComingBizzEvents = filterUpcomingEvents(formatedBusinessEvent);

      upComingEvents = [...upComingUserEvents, ...upComingBizzEvents];
      LocalTimeZoneEvents = [...formatedEvent, ...formatedBusinessEvent];
    }

    return { upComingEvents, LocalTimeZoneEvents };
  }, [events]);

  return {
    apiCalendars: calendars,
    apiEvents: events,
    loading: loadingCalendars || loadingEvents,
    calendarQuery,
    eventsQuery,
    events: LocalTimeZoneEvents,
    upComingEvents,
    TodoList,
    upComingTodoList,
  };
};
