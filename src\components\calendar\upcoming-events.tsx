import { BizzEventModelT, EventModelT, TodoListModelT } from "@/types";
import BgImage from "@/assets/images/bg-image.png";
import DateGroupEventList from "@/components/common/event/date-wise-group-events";

export default function UpComingEvents({
  events,
  TodoList,
}: {
  events: (EventModelT | BizzEventModelT)[];
  TodoList: TodoListModelT[];
}) {
  return (
    <div className="overflow-hidden flex flex-col h-full">
      {/* Calendar Header */}
      <div className="flex items-center mx-auto gap-2 w-fit">
        <h2 className="text-sm font-semibold">Upcoming Events</h2>
      </div>
      <div className="relative overflow-auto flex-1 bg-white rounded-12px">
        <img
          className="w-full h-full absolute inset-0 opacity-40"
          src={BgImage}
          alt=""
        />
        <div className="relative  w-full h-full flex flex-col p-6  overflow-y-auto no-scrollbar z-10">
          <DateGroupEventList events={events} Todos={TodoList} />
        </div>
      </div>
    </div>
  );
}
