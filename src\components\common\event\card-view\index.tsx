import { BizzEventModelT, EventModelT, TodoListModelT } from "@/types";
import HeartIcon from "@/assets/svg/heart-icon.svg";
import MessageIcon from "@/assets/svg/message-icon.svg";
import { formatTimeOnly } from "@/utils";
import { Image } from "@/components/ui/image";
import { ToDoStatusValue } from "@/enums";
import { cn } from "@/lib/utils";
import { Flame } from "lucide-react";
import { TodoSmileIcon } from "@/assets/svg/icons";
import { Link } from "react-router";

export function EventMiniCardView({
  start,
  end,
  calendarName,
  title,
  picture,
  _id,
}: EventModelT) {
  return (
    <Link to={`/event/${_id}`}>
      <div className="rounded-xl overflow-hidden bg-fb-cadelBlue flex w-full drop-shadow-cardShadow shadow-cardInnerShadow">
        <div className="bg-fb-bPrime-border w-3" />
        <div className="py-2 px-3 flex flex-col w-full gap-0.5">
          <div className="text-black text-xs">
            {formatTimeOnly(start)}-{formatTimeOnly(end)}
          </div>
          <div className="flex gap-2 items-center w-full">
            <div className="h-8 w-8 min-w-8 lg:h-10 lg:w-10 lg:min-w-10 rounded-10px overflow-hidden">
              <Image src={picture} alt="" />
            </div>
            <div className="flex flex-col w-full">
              <div>
                <p className="text-xs xl:text-sm font-medium !leading-4">
                  {title}
                </p>
                <p className="text-xxxs xl:text-xxs font-light italic">
                  {calendarName}
                </p>
              </div>
              {/* <div className="flex gap-1 items-center text-black text-xs w-full"> */}
              {/* <div className="w-full h-[1px] rounded-full bg-black/70" /> */}
              {/* <div className="flex items-center  min-w-fit gap-0.5">
                <button className="flex items-center gap-0.5">
                  {Impression.length}
                  <img src={HeartIcon} alt="" className="w-3.5 h-3.5" />
                </button>
               
                <button className="px-1 border rounded-full border-black text-xxs italic">
                  Attending?
                </button>
                <button className="flex items-center gap-0.5">
                  {Comments.length}{" "}
                  <img src={MessageIcon} alt="" className="w-3.5 h-3.5" />
                </button>
              </div> */}
              {/* </div> */}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function BizzEventMiniCardView({
  start,
  end,
  calendarName,
  Comments,
  title,
  picture,
  Impression,
  _id,
}: BizzEventModelT) {
  return (
    <Link to={`/event/${_id}`}>
      <div className="rounded-xl overflow-hidden bg-fb-cadelBlue flex w-full drop-shadow-cardShadow shadow-cardInnerShadow">
        <div className="bg-fb-bPrime-border w-3" />
        <div className="py-2 px-3 flex flex-col w-full gap-0.5">
          <div className="text-black text-xs">
            {formatTimeOnly(start)}-{formatTimeOnly(end)}
          </div>
          <div className="flex gap-2 items-center w-full">
            <div className="h-8 w-8 min-w-8 lg:h-10 lg:w-10 lg:min-w-10 rounded-10px overflow-hidden">
              <Image src={picture} alt="" />
            </div>
            <div className="flex flex-col w-full">
              <div>
                <p className="text-xs xl:text-sm font-medium !leading-4">
                  {title}
                </p>
                <p className="text-xxxs xl:text-xxs font-light italic">
                  {calendarName}
                </p>
              </div>
              <div className="flex gap-1 items-center text-black text-xs w-full">
                <div className="w-full h-[1px] rounded-full bg-black/70" />
                <div className="flex items-center  min-w-fit gap-0.5">
                  <button className="flex items-center gap-0.5">
                    {Impression.length}
                    <img src={HeartIcon} alt="" className="w-3.5 h-3.5" />
                  </button>
                  <button className="px-1 border rounded-full border-black text-xxs italic">
                    Attending?
                  </button>
                  <button className="flex items-center gap-0.5">
                    {Comments.length}{" "}
                    <img src={MessageIcon} alt="" className="w-3.5 h-3.5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function EventListCardView({
  start,
  end,
  calendarName,
  title,
  picture,
  allDayEvent,
  _id,
}: EventModelT) {
  return (
    <Link to={`/event/${_id}`}>
      <div className="rounded-xl overflow-hidden bg-fb-cadelBlue flex w-full drop-shadow-cardShadow shadow-cardInnerShadow">
        <div className="bg-fb-bPrime-border w-3" />
        <div className="px-5 py-2 flex w-full gap-3 items-center">
          {allDayEvent ? (
            <div className="flex flex-col">
              <p className="text-xs font-medium -tracking-wide">All Day</p>
              <p className=" whitespace-nowrap text-xxs">
                {formatTimeOnly(start)}
              </p>
            </div>
          ) : (
            <div className="flex flex-col">
              <p className="text-xs font-medium -tracking-wide">
                {formatTimeOnly(start)}
              </p>
              <p className=" whitespace-nowrap text-xxs">
                to {formatTimeOnly(end)}
              </p>
            </div>
          )}
          <div className="h-full w-[1px] bg-fb-neutral-700 rounded-full" />
          <div className="flex gap-3 items-center w-full">
            <div className="h-12 w-12 min-w-12 rounded-lg overflow-hidden">
              {/* <img src={picture} alt="" className="w-full h-full object-cover" /> */}
              <Image src={picture} alt="" />
            </div>
            <div className="flex flex-col gap-1.5 w-full">
              <div>
                <p className="text-sm font-medium leading-4">{title}</p>
                <p className="text-xxs font-light italic">{calendarName}</p>
              </div>
              {/* <div className="flex gap-1 items-center text-black text-xs w-full"> */}
              {/* <div className="w-full h-[1px] rounded-full bg-black/70" /> */}
              {/* <div className="flex items-center  min-w-fit gap-2">
                <button className="flex items-center gap-0.5">
                  {Impression.length}
                  <img src={HeartIcon} alt="" className="w-3.5 h-3.5" />
                </button>
                <button className="px-1 py-[1px] border rounded-full border-black text-xxs italic">
                  Attending?
                </button>
                <button className="flex items-center gap-0.5">
                  {Comments.length}
                  <img src={MessageIcon} alt="" className="w-3.5 h-3.5" />
                </button>
              </div> */}
              {/* </div> */}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function BizzEventListCardView({
  start,
  end,
  calendarName,
  Comments,
  title,
  picture,
  Impression,
  allDayEvent,
  _id,
}: BizzEventModelT) {
  return (
    <Link to={`/event/${_id}`}>
      <div className="rounded-xl overflow-hidden bg-fb-cadelBlue flex w-full drop-shadow-cardShadow shadow-cardInnerShadow">
        <div className="bg-fb-bPrime-border w-3" />
        <div className="px-5 py-2 flex w-full gap-3 items-center">
          {allDayEvent ? (
            <div className="flex flex-col">
              <p className="text-xs font-medium -tracking-wide">All Day</p>
              <p className=" whitespace-nowrap text-xxs">
                {formatTimeOnly(start)}
              </p>
            </div>
          ) : (
            <div className="flex flex-col">
              <p className="text-xs font-medium -tracking-wide">
                {formatTimeOnly(start)}
              </p>
              <p className=" whitespace-nowrap text-xxs">
                to {formatTimeOnly(end)}
              </p>
            </div>
          )}
          <div className="h-full w-[1px] bg-fb-neutral-700 rounded-full" />
          <div className="flex gap-3 items-center w-full">
            <div className="h-12 w-12 min-w-12 rounded-lg overflow-hidden">
              {/* <img src={picture} alt="" className="w-full h-full object-cover" /> */}
              <Image src={picture} alt="" />
            </div>
            <div className="flex flex-col gap-1.5 w-full">
              <div>
                <p className="text-sm font-medium leading-4">{title}</p>
                <p className="text-xxs font-light italic">{calendarName}</p>
              </div>
              <div className="flex gap-1 items-center text-black text-xs w-full">
                <div className="w-full h-[1px] rounded-full bg-black/70" />
                <div className="flex items-center  min-w-fit gap-2">
                  <button className="flex items-center gap-0.5">
                    {Impression.length}
                    <img src={HeartIcon} alt="" className="w-3.5 h-3.5" />
                  </button>
                  {/* <button className="flex items-center">
                  {Rsvps.length}
                  <img src={TagIcon} alt="" className="w-3.5 h-3.5" />
                </button> */}
                  <button className="px-1 py-[1px] border rounded-full border-black text-xxs italic">
                    Attending?
                  </button>
                  <button className="flex items-center gap-0.5">
                    {Comments.length}
                    <img src={MessageIcon} alt="" className="w-3.5 h-3.5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

export const TodoCardView = ({
  task,
  status,
  className,
  priorityImg,
}: TodoListModelT & { className?: string }) => {
  return (
    <div
      className={cn(
        "bg-white whitespace-nowrap overflow-hidden gap-1 flex px-2 rounded-full py-1.5 items-center shadow-md",
        className
      )}>
      <TodoSmileIcon className="text-fb-bPrime-smile size-5" />
      <p className="truncate text-xs font-semibold">{task}</p>
      <Flame
        className={cn(
          "size-4 ml-auto fill-fb-option-3",
          priorityImg === ToDoStatusValue.URGENT && "fill-fb-option-4",
          priorityImg === ToDoStatusValue.NO_IMPORTANT && "fill-fb-option-3",
          priorityImg === ToDoStatusValue.COMPLETED && "fill-fb-option-7",
          status === ToDoStatusValue.COMPLETED && "fill-fb-option-7"
        )}
        strokeWidth={1.3}
      />
    </div>
  );
};
