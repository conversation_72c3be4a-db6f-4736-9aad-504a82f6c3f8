export const API_ENDPOINTS = {
  event: {
    getAll: "/api/agendas/fetchBuisnessAgendas",
    create: "/api/agendas/create",
    update: "/api/agendas/update",
    details: "/api/agendas/eventDetailsById",
    getUserEvents: "/api/agendas/fetchUserEvents",
  },
  calendar: {
    getAll: "/api/calendars/fetchCalendarEndUser",
    singleOne: "/api/calendars/fetch-biz-calendar",
    create: "/api/calendars/create",
    update: "/api/calendars/update-calendar",
  },
  account: {
    get: "/api/accounts/fetchAccDetailsEu",
    update: "/api/accounts/updateAccountDetails",
    create: "/api/accounts/accSetupEu",
  },
  todo: {
    create: "/api/todoDev/addTaskEU", // ✅
    update: "/api/todoDev/updateTaskEU", // ✅
    delete: "/api/todoDev/deleteTaskEU", // ✅
    updateStatus: "/api/todoDev/updateTaskStatusEU", // ✅
    categoryAddUpdate: "/api/todoDev/updateCategoriesEU", // ✅
  },
  feedBack: { add: "/api/others/feedback" },
  user: {
    login: "/api/users/userDetails",
    update: "/api/users/update-user-details",
    fetchUserIds: "/api/users/fetch-user-ids",
  },
  imageUpload: {
    upload: "/api/file/upload",
  },
  notification: {
    getAll: "/api/notifications/getNotificationsByRecipientUsrId",
    markAsRead: "/api/notifications/markNotificationsAsRead",
  },
  bizCalendar: {
    getAll: "/api/calendars/fetch-biz-calendar", // ✅
    follow: "/api/calendars/follow-biz-calendar", // ✅
    unfollow: "/api/calendars/unfollow-biz-calendar",
  },
  comment: {
    create: "/api/others/addComent",
    delete: "/api/others/deleteComment",
    report: "/api/others/reportComment",
    edit: "/api/others/editComment",
  },
  reply: {
    create: "/api/others/addReply",
    delete: "/api/others/deleteReply",
    report: "/api/others/reportReply",
    edit: "/api/others/editReply",
  },
  eventAction: {
    impression: "/api/others/addImpression",
    rsvp: "/api/others/addRSVP",
  },
};

// ## DONE with test
