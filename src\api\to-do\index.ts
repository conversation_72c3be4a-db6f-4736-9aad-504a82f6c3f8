import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";
import {
  CreateToDoFormT,
  DeletTodoFormT,
  StatusCodes,
  UpadteStatusFormT,
} from "@/types/api";
import { Query_Key } from "@/constant/data";

export const useCreateToDo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_ToDo,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useUpdateToDo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_ToDo,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useCategoryUpCre = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ data, accId }: { data: string[]; accId: string }) =>
      Api_Create_Update_Todo_Category(accId, data),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useUpdateTodoStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Todo_Status,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useDeleteTodo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Delete_Todo,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

const Api_Create_ToDo = async (
  data: CreateToDoFormT
): Promise<CreateToDoFormT> => {
  const FailureMessage = "Failed to create todo...";

  try {
    const response = await API.post(API_ENDPOINTS.todo.create, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_ToDo = async (
  data: CreateToDoFormT
): Promise<CreateToDoFormT> => {
  const FailureMessage = "Failed to update todo...";

  try {
    const response = await API.post(API_ENDPOINTS.todo.update, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Create_Update_Todo_Category = async (
  accId: string,
  data: string[]
): Promise<string[]> => {
  const FailureMessage = "Failed to change category...";

  const daata = { _id: accId, category: data };

  try {
    const response = await API.post(
      API_ENDPOINTS.todo.categoryAddUpdate,
      daata
    );
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_Todo_Status = async (
  data: UpadteStatusFormT
): Promise<null> => {
  const FailureMessage = "Failed to update status...";

  try {
    const response = await API.post(API_ENDPOINTS.todo.updateStatus, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Delete_Todo = async (data: DeletTodoFormT): Promise<null> => {
  const FailureMessage = "Failed to delete todo...";

  try {
    const response = await API.post(API_ENDPOINTS.todo.delete, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
