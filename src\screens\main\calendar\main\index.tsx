import { Link } from "react-router";

import { CalendarModelT } from "@/types";
import BgImage from "@/assets/images/bg-image.png";
import ArrowRound from "@/assets/images/arrow-round.png";
import ArrowUpway from "@/assets/images/arrow-upway.png";
import BussinessInfo from "@/components/common/bussiness-info";
import CalendarNoneImg from "@/assets/images/calendar-none.png";
import CalendarThumpImg from "@/assets/images/calendar-thump.png";
import MonthCalendarDateView from "@/components/calendar/month-calendar-date-view";
import { useGetAllCalendar } from "@/api/calendar";
import { useAuth } from "@/context/AuthContext";

export default function CalendarScreen() {
  const { userId } = useAuth();

  const { data } = useGetAllCalendar({ usrId: userId });

  const calendarData = data || [];

  return (
    <div className="flex flex-col w-full h-dvh">
      <div className="flex justify-between">
        {/* screen name */}
        <div className="flex w-full px-6">
          <p className="text-base lg:text-xl mt-auto">All your calendars</p>
        </div>
        <div className="w-52 lg:w-72 ml-auto pt-2">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex flex-col gap-0.5 w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] pb-3 pr-3 mt-2">
        <div className="bg-white w-full h-full rounded-2xl overflow-y-auto no-scrollbar">
          {calendarData.length === 0 ? (
            <div className="w-full h-full flex relative justify-center items-center">
              <img
                className="w-full h-full absolute inset-0 opacity-40"
                src={BgImage}
                alt=""
              />
              <div className="flex flex-col gap-2 w-80 justify-center items-center relative">
                <div className="w-64 h-64 flex flex-col bg-white shadow-calendarInnerShadow drop-shadow-calendarShadow justify-center items-center gap-2 p-4 rounded-xl z-10">
                  <img src={CalendarNoneImg} alt="" className="w-40 h-40" />

                  <Link
                    to={"/calendar/add-calendar"}
                    className="h-8 rounded-full text-base flex justify-center items-center bg-fb-bPrime-600 hover:bg-primary/90 w-full text-center text-white">
                    Create{" "}
                  </Link>
                </div>
                <img
                  src={ArrowRound}
                  alt=""
                  className="absolute right-60 bottom-32 w-28 h-20 "
                />
                <img
                  src={ArrowUpway}
                  alt=""
                  className="absolute left-60 top-3 w-28 h-20 opacity-45"
                />
                <p className="text-center font-semibold text-xl lg:text-2xl leading-8">
                  You do not have any calendars yet
                </p>
              </div>
            </div>
          ) : (
            <div className="w-full grid grid-cols-1 p-3 gap-3  lg:p-6 sm:grid-cols-2  xl:grid-cols-3 2xl:grid-cols-4 ">
              <NeedNewCalendar />
              {calendarData.map((row, ind) => {
                return <CalendarView data={row} key={ind} />;
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

const CalendarView = ({ data }: { data: CalendarModelT }) => {
  const { color, calendarName, _id } = data;

  const cuurentCalendar = {
    month: new Date().getMonth(),
    year: new Date().getFullYear(),
  };

  return (
    <Link to={_id}>
      <div className="p-2 lg:p-4 h-full flex gap-2 rounded-xl bg-white  shadow-calenViewShadow h-auto items-center">
        <div
          className="flex flex-col w-28 lg:min-w-32 lg:w-32 gap-3 h-full justify-center items-center px-2 rounded-xl"
          style={{ backgroundColor: color }}>
          <div className="text-center max-w-32">
            <p className="text-sm lg:text-lg font-medium truncate px-2">
              {calendarName}
            </p>
          </div>
        </div>
        <MonthCalendarDateView
          month={cuurentCalendar.month}
          year={cuurentCalendar.year}
          isSmallView
          className="text-xxs lg:text-xs w-full"
        />
      </div>
    </Link>
  );
};

const NeedNewCalendar = () => {
  return (
    <div className="p-4 flex gap-2 rounded-xl bg-white  shadow-calenViewShadow h-60 items-center">
      <img src={CalendarThumpImg} alt="" className="h-32 md:h-40" />
      <div className="w-full text-center flex flex-col gap-2 px-3">
        <p className="text-xl md:text-2xl font-medium">Need a new calendar?</p>
        <Link
          to={"/calendar/add-calendar"}
          className="h-8 rounded-full flex justify-center items-center bg-fb-bPrime-600 hover:bg-primary/90 w-full text-center text-white">
          Create{" "}
        </Link>
      </div>
    </div>
  );
};
