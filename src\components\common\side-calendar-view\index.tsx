import MonthCalendarDateView from "@/components/calendar/month-calendar-date-view";

export default function SideCalendarView() {
  const cuurentCalendar = {
    month: new Date().getMonth(),
    year: new Date().getFullYear(),
  };

  return (
    <div className="w-full max-w-md mx-auto bg-fb-neutral-50 rounded-xl shadow-calendarInnerShadow drop-shadow-calendarShadow p-4 mt-auto">
      <MonthCalendarDateView
        month={cuurentCalendar.month}
        year={cuurentCalendar.year}
        className="text-xxs lg:text-xs sticky bottom-0"
        weekDayClassName="leading-8"
      />
    </div>
  );
}
