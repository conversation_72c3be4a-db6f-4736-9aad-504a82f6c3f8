import { useState } from "react";
import { format, addYears, subYears } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { cn } from "@/lib/utils";
import MonthCalendarDateView from "./month-calendar-date-view";

export default function YearCalendar() {
  const [currentYear, setCurrentYear] = useState(new Date());

  // Navigate to previous year
  const goToPreviousYear = () => {
    setCurrentYear(subYears(currentYear, 1));
  };

  // Navigate to next year
  const goToNextYear = () => {
    setCurrentYear(addYears(currentYear, 1));
  };
  //  h-[calc(100dvh-64px)]
  return (
    <div className="overflow-hidden flex flex-col">
      {/* Calendar Header */}
      <div className="flex items-center mx-auto gap-2 w-fit">
        <button
          onClick={goToPreviousYear}
          className="p-0.5 rounded-full hover:bg-gray-100">
          <ChevronLeft className="h-4 w-4" />
        </button>
        <h2 className="text-sm font-semibold">{format(currentYear, "yyyy")}</h2>
        <button
          onClick={goToNextYear}
          className="p-0.5 rounded-full hover:bg-gray-100">
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="bg-white w-full h-full flex p-2 rounded-2xl overflow-y-auto no-scrollbar">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 h-fit w-full justify-between">
          {Array.from({ length: 12 }).map((_, index) => {
            const isMonth =
              index === new Date().getMonth() &&
              currentYear.getFullYear() === new Date().getFullYear();
            return (
              <div
                className={cn(
                  "rounded-xl shadow-calendarInnerShadow drop-shadow-calendarShadow p-2.5 h-50",
                  isMonth ? "bg-fb-bPrime-50" : "bg-white "
                )}
                key={index}>
                <MonthCalendarDateView
                  month={index}
                  year={currentYear.getFullYear()}
                  className="  flex flex-col text-xxxs lg:text-xxs 2xl:text-xs"
                  key={index}
                  isSmallView
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
