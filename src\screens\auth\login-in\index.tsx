import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { Link, Navigate } from "react-router";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import GmailPng from "@/assets/images/Gmail.png";
import ApplePng from "@/assets/images/apple.png";
import BgImage from "@/assets/images/bg-image.png";
import FacebookPng from "@/assets/images/facebook.png";
import UserLogo from "@/assets/images/fragment-user-logo.png";
import { useAuth } from "@/context/AuthContext";
import {
  useAppleLogin,
  useEmailLogin,
  useFacebookLogin,
  useGoogleLogin,
} from "@/api/auth";

export default function LoginScreen() {
  const { userId } = useAuth();

  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");

  const emailLogin = useEmailLogin();
  const googleLogin = useGoogleLogin();
  const appleLogin = useAppleLogin();
  const facebookLogin = useFacebookLogin();

  const handleEmailLogin = (e: FormEvent) => {
    e.preventDefault();
    emailLogin.mutate(
      { email, password },
      {
        onSuccess: (user) => {
          console.log("Logged in as:", user);
          toast.success("Login email-pass");
        },
        onError: (error) => {
          toast.error(error.message);
        },
      }
    );
  };

  const handleGoogleLogin = () => {
    googleLogin.mutate(undefined, {
      onSuccess: (user) => {
        console.log("Google logged in:", user);
        toast.success("Login email");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  };
  const handleAppleLogin = () => {
    appleLogin.mutate(undefined, {
      onSuccess: (user) => {
        console.log("Apple logged in:", user);
        toast.success("Login apple");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  };
  const handleFacebookLogin = () => {
    facebookLogin.mutate(undefined, {
      onSuccess: (user) => {
        console.log("Facebook logged in:", user);
        toast.success("Login facebook");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  };

  if (userId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-white flex w-full h-dvh sm:h-screen relative overflow-hidden">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />
      <div className="flex justify-center items-center w-1/2 z-10">
        <div className="flex flex-col">
          <div className=" h-48 mx-auto">
            <img src={UserLogo} alt="" className="h-full object-cover" />
          </div>
          <p className="text-3xl font-bold text-black">your one step</p>
          <p className="text-[44px] leading-none font-bold text-black">
            Organiser
          </p>
        </div>
      </div>
      <div className="flex h-full px-4 py-6 w-1/2 z-10 ">
        <div className="bg-fb-neutral-100 flex w-full rounded-3xl px-6 sm:px-12 md:px-16 lg:px-24 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <form
            className="flex flex-col gap-14 w-full justify-center"
            onSubmit={handleEmailLogin}>
            <div className="flex flex-col gap-3 w-full   h-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="email"
                  className="text-black font-semibold text-2xl">
                  Email Id*
                </Label>
                <Input
                  id="email"
                  value={email || ""}
                  type="email"
                  placeholder="<EMAIL>"
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-black bg-fb-neutral-50"
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="password"
                  className="text-black font-semibold text-2xl">
                  Password*
                </Label>
                <Input
                  id="password"
                  value={password || ""}
                  type="password"
                  placeholder="********"
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-black bg-fb-neutral-50"
                />
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button
                  type="submit"
                  className="text-xl font-semibold rounded-full text-white bg-fb-bPrime-600">
                  Log In
                </Button>
                <div className="flex items-center justify-between px-3">
                  <p className="text-black font-light ">
                    Do not have an account?{" "}
                    <Link to={"/signup"} className="">
                      Sign Up
                    </Link>
                  </p>
                  <Link to={"#"} className="text-black">
                    Forgot Password
                  </Link>
                </div>
              </div>
              <div className="flex justify-center items-center gap-2 opacity-50">
                <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
                or <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
              </div>
              <div className="flex flex-col gap-2 w-full items-center">
                <p className="text-black">Log in with</p>
                <div className="flex items-center justify-between gap-5 w-full">
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleGoogleLogin}>
                    <img src={GmailPng} alt="gmail" className="p-1.5 h-10" />
                    Gmail
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-900 flex text-white  gap-1 hover:bg-neutral-800 rounded-full"
                    onClick={handleAppleLogin}>
                    <img src={ApplePng} alt="apple" className="p-1.5 h-10" />
                    Apple Id
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-full bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleFacebookLogin}>
                    <img
                      src={FacebookPng}
                      alt="facebook"
                      className="p-1.5 h-10"
                    />
                    Facebook
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
