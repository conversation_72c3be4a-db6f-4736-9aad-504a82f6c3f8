import { useMemo } from "react";

import { BizzEventModelT, EventModelT } from "@/types";
import { useGetAllEvents } from "@/api/event";
import {
  mapRawBizzEventsToModel,
  mapRawUserEventsToModel,
} from "@/utils/model";
import { useGetAllCalendar } from "@/api/calendar";
import { convertEventTimesToLocal, filterUpcomingEvents } from "@/utils";
import { useAuth } from "@/context/AuthContext";
import { useGetAllBizzCalendar } from "@/api/biz-calendar";

export const useCalendarDetailsWithEvents = ({
  calendarId,
}: {
  calendarId?: string;
}) => {
  const { userId } = useAuth();

  const { data: calendars = [], isLoading: loadingCalendars } =
    useGetAllCalendar({
      usrId: userId || undefined,
    });

  const { data: bizzCalendars = [] } = useGetAllBizzCalendar();

  const calendar = useMemo(() => {
    const calenders = [...bizzCalendars, ...calendars];

    return calenders?.find((calendar) => calendar._id === calendarId);
  }, [calendars, calendarId, bizzCalendars]);

  const { data: events, isLoading: loadingEvents } = useGetAllEvents({
    userId,
  });

  const { upComingEvents, LocalTimeZoneEvents } = useMemo(() => {
    let upComingEvents: (EventModelT | BizzEventModelT)[] = [];
    let LocalTimeZoneEvents: (EventModelT | BizzEventModelT)[] = [];

    if (events) {
      const { businessEvents, endUserEvents } = events;

      const formatedEvent = convertEventTimesToLocal(
        mapRawUserEventsToModel(endUserEvents)
      );

      const formatedBusinessEvent = convertEventTimesToLocal(
        mapRawBizzEventsToModel(businessEvents)
      );

      const upComingUserEvents = filterUpcomingEvents(formatedEvent);
      const upComingBizzEvents = filterUpcomingEvents(formatedBusinessEvent);

      upComingEvents = [...upComingUserEvents, ...upComingBizzEvents];
      LocalTimeZoneEvents = [...formatedEvent, ...formatedBusinessEvent];
    }

    const calenderEvents = LocalTimeZoneEvents.filter(
      (event) => event.calId === calendarId
    );

    const upComingCalenderEvents = upComingEvents.filter(
      (event) => event.calId === calendarId
    );

    return {
      upComingEvents: upComingCalenderEvents,
      LocalTimeZoneEvents: calenderEvents,
    };
  }, [events, calendarId]);

  return {
    apiCalendars: calendar,
    loading: loadingCalendars || loadingEvents,
    events: LocalTimeZoneEvents,
    upComingEvents,
  };
};
