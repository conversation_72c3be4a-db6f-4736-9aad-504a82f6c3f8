import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export function AlertPopup({
  isOpen,
  title,
  onCLose,
  onSuccess,
}: {
  isOpen: boolean;
  title: string;
  onCLose: () => void;
  onSuccess: () => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onCLose}>
      <DialogContent className="sm:max-w-[500px] min-h-56 !rounded-xl">
        <DialogHeader>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-3 py-4">
          <p className="text-center text-lg lg:text-2xl leading-6 font-semibold">
            {title}
          </p>
        </div>
        <DialogFooter className="!justify-center flex  w-full items-center gap-2">
          <Button
            className={cn(
              "w-40  rounded-lg bg-fb-success-600 hover:bg-lime-700 drop-shadow-buttonShadow h-9"
            )}
            onClick={onCLose}>
            No
          </Button>
          <Button
            className={cn(
              "w-40  rounded-lg bg-fb-warn-600 hover:bg-red-700 drop-shadow-buttonShadow h-9"
            )}
            onClick={onSuccess}>
            Yes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
