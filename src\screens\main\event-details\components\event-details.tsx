import {
  BookmarkMinus,
  Flag,
  Heart,
  MapPin,
  SendHorizonal,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router";

import EditIcon from "@/assets/svg/edit-icon.svg";
import MessageIcon from "@/assets/svg/message-icon.svg";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { CommentModelT, EventModelT, SubCommentModelT } from "@/types";
import { UserDummyEventsLike } from "@/constant/dummy";
import { useGetAccounDetails } from "@/api/account";
import { useAuth } from "@/context/AuthContext";
import {
  useCreateComment,
  useCreateSubComment,
  useReportComment,
  useReportSubComment,
} from "@/api/event/action";
import { CreateCommentFormT, CreateSubCommentFormT } from "@/types/api";
import { formatEventDate, generateUUID } from "@/utils";
import { useUpdateEvent } from "@/api/event";

export default function EventDetailsView({
  data,
}: {
  data: EventModelT | null;
}) {
  const [currentView, setCurrentView] = useState("Comments");
  const navigation = useNavigate();

  const { mutate: deleteEvent, isPending } = useUpdateEvent();

  const handleDeleteEvent = () => {
    deleteEvent(
      { _id: data?._id || "", deleted: true },
      {
        onSuccess() {
          toast.success("Event deleted successfully");
          navigation(-1);
        },
        onError(error) {
          toast.error(error.message);
        },
      }
    );
  };

  return (
    <div className="flex flex-col gap-4 w-full pt-1" key={data?._id}>
      <div className="w-full h-auto flex gap-4">
        <div className="w-full flex flex-col gap-2">
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-xl -tracking-wide leading-3">
              {data?.title}
            </p>
            <Separator className="bg-fb-bPrime-hgts" />
            <div className="flex gap-4 items-center">
              <div className="h-8 w-8 rounded-full overflow-hidden">
                <img
                  src={data?.picture || "https://picsum.photos/250"}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex flex-col gap-0.5">
                <p className="text-sm tracking-wide leading-4">
                  {data?.calendarName}
                </p>
                <p className="text-xs font-light leading-4">{data?.calId}</p>
              </div>
            </div>
          </div>
          <div className="flex font-semibold text-lg -tracking-wide">
            <p>{formatEventDate(data?.start || "")?.date}</p>-
            <p>{formatEventDate(data?.end || "")?.date}</p>
          </div>
          <div className="flex flex-col gap-1">
            <p className="font-semibold text-sm text-black/75">About Event</p>

            <div
              className="prose"
              dangerouslySetInnerHTML={{
                __html: data?.description || "No description",
              }}
            />
          </div>
          <div className="flex gap-1 items-center mt-auto">
            <MapPin className="size-4" />{" "}
            <p className="font-semibold text-base">{data?.location}</p>
          </div>
          <div className="flex gap-4 w-full mt-auto">
            <Button
              className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              onClick={() => navigation(`/edit-event/${data?._id}`)}>
              <img src={EditIcon} alt="" className="w-4 h-4" /> Edit
            </Button>
            <Button
              disabled={isPending}
              className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-red-500 drop-shadow-buttonShadow"
              onClick={handleDeleteEvent}>
              <Trash2 className="w-4 h-4 text-red-500" /> Delete
            </Button>
          </div>
        </div>
        <div className="w-auto min-w-80 h-full max-h-80 rounded-lg overflow-hidden">
          <img
            src={data?.picture || ""}
            alt=""
            className="w-full h-full object-cover"
          />
        </div>
      </div>
      {/* view array */}
      <div className="flex gap-2 justify-between">
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            setCurrentView("Comments");
          }}>
          <img src={MessageIcon} alt="" className="w-4" />
          {data?.Comments?.length || 0}{" "}
          <p
            className={cn(
              currentView === "Comments"
                ? "font-semibold underline"
                : "font-normal"
            )}>
            Comments
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            setCurrentView("Likes");
          }}>
          <Heart className="fill-fb-pink size-4" />
          {data?.Impression?.length || 0}{" "}
          <p
            className={cn(
              currentView === "Likes"
                ? "font-semibold underline"
                : "font-normal"
            )}>
            Likes{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            setCurrentView("Attending");
          }}>
          <BookmarkMinus className="fill-fb-uPrime-300 size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              currentView === "Attending"
                ? "font-semibold underline"
                : "font-normal"
            )}>
            Attending{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            setCurrentView("deciding");
          }}>
          <BookmarkMinus className="fill-fb-purple size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              currentView === "deciding"
                ? "font-semibold underline"
                : "font-normal"
            )}>
            Still deciding{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            setCurrentView("notAttending");
          }}>
          <BookmarkMinus className="fill-fb-red-200 size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              currentView === "notAttending"
                ? "font-semibold underline"
                : "font-normal"
            )}>
            Not attending
          </p>
        </div>
      </div>

      {currentView === "Comments" && (
        <CommetsView
          eventId={data?._id || ""}
          comments={data?.Comments || []}
          replays={data?.Reply || []}
        />
      )}
      {currentView === "Likes" && <LikesView />}
      {currentView === "Attending" && <AttendingView />}
      {currentView === "deciding" && <DecidingView />}
      {currentView === "notAttending" && <NotAttendingView />}
    </div>
  );
}

const CommetsView = ({
  eventId,
  comments,
  replays,
}: {
  eventId: string;
  comments: CommentModelT[];
  replays: SubCommentModelT[];
}) => {
  const { userId } = useAuth();

  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  const { mutate: createComment } = useCreateComment();
  // const { mutate: updateComment } = useUpdateComment();
  // const { mutate: deleteComment } = useDeleteComment();
  const { mutate: reportComment } = useReportComment();
  const { mutate: createSubComment } = useCreateSubComment();
  const { mutate: reportSubComment } = useReportSubComment();

  const [subComment, setSubComment] = useState("");
  const [subCommentId, setSubCommentId] = useState("");

  const [comment, setComment] = useState("");

  const handleCreateComment = () => {
    if (comment.trim() === "") return toast.info("Comment is required");

    if (userId === null) return toast.info("Please login to comment");

    const data: CreateCommentFormT = {
      agendaId: eventId,
      comment: comment,
      usrId: userId || "",
      displayName: AccountData?.accData?.[0]?.userName || "",
      picture: AccountData?.accData?.[0]?.picture || "",
      commentId: generateUUID("comment"),
      Itemstart: new Date().toISOString(),
    };

    createComment(data, {
      onSuccess() {
        toast.success("Comment created successfully");
        setComment("");
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  const handleReportComment = (commentId: string) => {
    if (userId === null) return toast.info("Please login to comment");

    reportComment(
      {
        agendaId: eventId,
        commentId: commentId,
        usrId: userId || "",
        reason: "Inappropriate content",
      },
      {
        onSuccess() {
          toast.success("Comment reported successfully");
          setComment("");
        },
        onError(error) {
          toast.error(error.message);
        },
      }
    );
  };

  const handleCreateSubComment = (commentId: string) => {
    if (subComment.trim() === "") return toast.info("Sub Comment is required");

    if (userId === null) return toast.info("Please login to comment");

    const data: CreateSubCommentFormT = {
      agendaId: eventId,
      replyId: generateUUID("reply"),
      reply: subComment,
      usrId: userId || "",
      displayName: AccountData?.accData?.[0]?.userName || "",
      picture: AccountData?.accData?.[0]?.picture || "",
      commentId: commentId,
      Itemstart: new Date().toISOString(),
    };

    createSubComment(data, {
      onSuccess() {
        toast.success("Sub Comment created successfully");
        setSubComment("");
        setSubCommentId("");
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  const handleReportSubComment = (commentId: string) => {
    if (userId === null) return toast.info("Please login to comment");

    reportSubComment(
      {
        agendaId: eventId,
        replyId: commentId,
        usrId: userId || "",
        reason: "Inappropriate content",
      },
      {
        onSuccess() {
          toast.success("Sub Comment reported successfully");
        },
        onError(error) {
          toast.error(error.message);
        },
      }
    );
  };

  const getSubComment = (commentId: string) => {
    return replays.filter((row) => row.commentId === commentId);
  };

  return (
    <div className=" w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50">
      <p className="text-base font-semibold">Comments</p>
      <div className="flex flex-col gap-2 px-3 py-2">
        {comments.map((row, ind) => {
          return (
            <div className="flex flex-col gap-1 " key={ind}>
              <div
                key={ind}
                className="flex items-center gap-2 border-b border-black/60 pb-1.5">
                <img
                  src={row.picture || "https://picsum.photos/250"}
                  alt=""
                  className="h-8 w-8 overflow-hidden rounded-full object-cover"
                />
                <div className="text-xs leading-4">
                  <p className="text-fb-neutral-900">{row.displayName}</p>
                  <p className="text-fb-neutral-800">{row.comment}</p>
                </div>
                <div className="flex gap-2 ml-auto items-center">
                  <button onClick={() => setSubCommentId(row.commentId)}>
                    <img src={MessageIcon} alt="" className="w-3" />
                  </button>
                  <Heart className="size-3" />
                  <button onClick={() => handleReportComment(row.commentId)}>
                    <Flag
                      className={cn("size-3", row.report && "fill-fb-warn-600")}
                    />
                  </button>
                </div>
              </div>
              {subCommentId === row.commentId && (
                <div className="flex flex-col gap-1 relative">
                  <Input
                    value={subComment}
                    onChange={(e) => setSubComment(e.target.value)}
                    placeholder="Type your comment here"
                    className="bg-[#64769330] h-7 border-none text-black rounded-lg !text-xs"
                  />
                  <Button
                    variant={"ghost"}
                    size={"icon"}
                    className="h-5 w-5 absolute top-1  right-2"
                    onClick={() => handleCreateSubComment(row.commentId)}>
                    <SendHorizonal className="size-4" />
                  </Button>
                </div>
              )}
              {getSubComment(row.commentId).map((row2, in2) => {
                return (
                  <div
                    key={in2}
                    className="flex items-center gap-2 ml-6 border-b border-black/60 pb-1.5">
                    <img
                      src={row2.picture || "https://picsum.photos/250"}
                      alt=""
                      className="h-8 w-8 overflow-hidden rounded-full object-cover"
                    />
                    <div className="text-xs leading-4">
                      <p className="text-fb-neutral-900">{row2.displayName}</p>
                      <p className="text-fb-neutral-800">{row2.reply}</p>
                    </div>
                    <div className="flex gap-2 ml-auto items-center">
                      {/* <img src={MessageIcon} alt="" className="w-3" /> */}
                      <Heart className="size-3" />
                      <button
                        onClick={() => handleReportSubComment(row2.replyId)}>
                        <Flag
                          className={cn(
                            "size-3",
                            row2.report && "fill-fb-warn-600"
                          )}
                        />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
      <div className="w-full relative">
        <Input
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Type your question here "
          className="bg-[#64769330] h-7 border-none text-black rounded-lg !text-xs"
        />
        <Button
          variant={"ghost"}
          size={"icon"}
          className="h-5 w-5 absolute top-1  right-2"
          onClick={handleCreateComment}>
          <SendHorizonal className="size-4" />
        </Button>
      </div>
    </div>
  );
};

const LikesView = () => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50">
      <p className="text-base font-semibold ">People who liked this event</p>
      <div className="flex flex-col gap-2 px-3 py-2">
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} />;
        })}
      </div>
    </div>
  );
};
const AttendingView = () => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50">
      <p className="text-base font-semibold ">People attending this event</p>
      <div className="flex flex-col gap-2 px-3 py-2">
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} />;
        })}
      </div>
    </div>
  );
};
const DecidingView = () => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50">
      <p className="text-base font-semibold ">
        People deciding about this event
      </p>
      <div className="flex flex-col gap-2 px-3 py-2">
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} />;
        })}
      </div>
    </div>
  );
};

const NotAttendingView = () => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50">
      <p className="text-base font-semibold ">
        People who not attending this event
      </p>
      <div className="flex flex-col gap-2 px-3 py-2">
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} />;
        })}
      </div>
    </div>
  );
};

const PersonDetails = ({ row }: { row: { name: string; image: string } }) => {
  return (
    <div className="flex items-center gap-2">
      <img
        src={row.image}
        alt=""
        className="h-5 w-5 overflow-hidden rounded-full object-cover"
      />
      <p className="text-sm">{row.name}</p>
      <div className="flex gap-2 ml-auto items-center">
        <img src={MessageIcon} alt="" className="w-4" />
        <Flag className="size-3.5" />
      </div>
    </div>
  );
};
