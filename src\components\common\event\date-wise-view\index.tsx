import { formatEventDate } from "@/utils";
import { EventModelT, BizzEventModelT } from "@/types";

type GroupedEvent = {
  date: string;
  data: (EventModelT | BizzEventModelT)[];
};

const groupEventsByDate = (
  events: (EventModelT | BizzEventModelT)[]
): GroupedEvent[] => {
  return events.reduce<GroupedEvent[]>((acc, event) => {
    const dateKey = new Date(event.calendarName).toDateString();

    const existingGroup = acc.find((group) => group.date === dateKey);

    if (existingGroup) {
      existingGroup.data.push(event);
    } else {
      acc.push({ date: dateKey, data: [event] });
    }

    return acc;
  }, []);
};

export default function EventsDateWise({
  events,
}: {
  events: (EventModelT | BizzEventModelT)[];
}) {
  const groupedEvents = groupEventsByDate(events);

  return (
    <div className="flex flex-col gap-3 ">
      {groupedEvents.map((row, index) => {
        const date = formatEventDate(row.date);
        return (
          <div key={index} className="flex flex-col gap-2 mb-1">
            <div className="flex gap-1 items-end border-b border-fb-bPrime-hgts">
              <p className="font-semibold text-base">{date.date},</p>
              <p className="font-light text-xs">({date.day})</p>
            </div>
            {/* {row.data.map((even, index) => {
              return <EventListCardView {...even} key={index} />;
            })} */}
          </div>
        );
      })}
    </div>
  );
}
