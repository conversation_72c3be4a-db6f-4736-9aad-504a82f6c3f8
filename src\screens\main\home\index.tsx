import { useEffect, useState } from "react";

import MoodNoteBlue from "@/components/common/mood-note-blue";
import YearCalendar from "@/components/calendar/year-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import MonthCalendar from "@/components/calendar/month-calendar";
import WeeklyCalendar from "@/components/calendar/weekly-calendar";
import SideCalendarView from "@/components/common/side-calendar-view";
import CalendarTabSwitcher, {
  AvailableCalendarTabs,
} from "@/components/calendar/calendar-tab-switch";
import UpComingEvents from "@/components/calendar/upcoming-events";
import EventsOnDateView from "@/components/common/event/events-on-date";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import { useAuth } from "@/context/AuthContext";
import { useSearchParams } from "react-router";
import AppLoader from "@/components/common/app-loader";

export default function HomeScreen() {
  const [searchParams, setSearchParams] = useSearchParams();

  const { userId } = useAuth();

  const initialTab =
    (searchParams.get("tab") as AvailableCalendarTabs) || "Upcoming";
  const [activeTab, setActiveTab] = useState<AvailableCalendarTabs>(initialTab);

  const { events, loading, upComingEvents, TodoList, upComingTodoList } =
    useCalendarsWithEvents(userId);

  useEffect(() => {
    // Sync state with URL when tab changes
    setSearchParams({ tab: activeTab });
  }, [activeTab, setSearchParams]);

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
  };

  if (loading) return <AppLoader />;

  return (
    <div className="flex w-full h-dvh">
      <div className="flex flex-col gap-2 py-3 w-[calc(100%-250px)] h-full">
        <CalendarTabSwitcher
          defaultTab={activeTab}
          onTabChange={handleTabChange}
        />

        {/* <div className="relative h-full flex-1 flex flex-col">
          <img
            className="w-full h-full absolute inset-0 opacity-40"
            src={BgImage}
            alt=""
          /> */}
        {activeTab === "Upcoming" && (
          <UpComingEvents events={upComingEvents} TodoList={upComingTodoList} />
        )}
        {activeTab === "Week" && (
          <WeeklyCalendar events={events} Todos={TodoList} />
        )}
        {activeTab === "Month" && <MonthCalendar events={events} />}
        {activeTab === "Year" && <YearCalendar />}
        {/* </div> */}
      </div>
      {/* events side data */}
      <div className="w-64 lg:w-80 px-4 py-3 flex flex-col gap-3">
        {/* bussiness details */}

        <BussinessInfo />
        <div className="flex w-full h-[calc(100dvh-66px)] lg:h-[calc(100dvh-72px)] justify-center items-center">
          {activeTab === "Upcoming" ? (
            <MoodNoteBlue text="Select an event to see details" />
          ) : (
            <EventsOnDateView />
          )}
        </div>

        {/* calendar view */}
        {activeTab === "Upcoming" && <SideCalendarView />}
      </div>
    </div>
  );
}
