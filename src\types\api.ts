import { BizzEventModelT, EventModelT, NotificationModelT, ZoneInfo } from ".";

export enum StatusCodes {
  OK = 200,
  CREATED = 201,
  DELETED = 204,
  RESENT = 302,
  EXISTS = 304,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  UNVERIFIED = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  SUSPENDED = 423,
  SERVER_ERROR = 500,
  UNAVAILABLE = 503,
}

export type ApiResponse<T> = {
  data: T | null;
  success: boolean;
  message: string;
  status: StatusCodes;
};

export type ApiErrorResponse = {
  data: null;
  success: false;
  message: string;
  status: StatusCodes;
};

export type CreatEventFormT = {
  _id: string;
  title: string;
  allDayEvent: boolean;
  start: Date;
  end: Date;
  repeat: boolean;
  frequency: string;
  repeatEvery: string;
  days: string[];
  repeatEndDate: Date;
  location: string;
  link: string | string[];
  picture: string;
  zone: ZoneInfo;
  attach: string | null;
  description: string;
  event: boolean;
  note: boolean;
  userType: "endUser";
  textColor: string;
  deleted: boolean;
  calendarName: string;
  calId: string;
  color: string;
  usrId: string;
  profileImage: File | null;
};

export type CreatCalendarFormT = {
  _id: string;
  calendarName: string;
  color: string;
  deleted: boolean;
  userType: string;
  usrId: string;
};

export type CreateToDoFormT = {
  _id: string;
  taskId: string;
  category: string;
  priorityImg: string;
  task: string;
  date: string;
};

export type UpadteStatusFormT = {
  _id: string;
  taskId: string;
  status: string;
};

export type DeletTodoFormT = {
  _id: string;
  taskId: string;
};

export type NotificationResponseT = {
  notifications: NotificationModelT[];
  totalCount: number;
  unreadCount: number;
};

export type NotificatinMarkAsReadFormT = {
  recipientUsrId: string | null;
  notificationIds: string[];
};

export type FollowBizzCalendarFormT = {
  usrId: string;
  calId: string;
};

export type GetAllEventsResT = {
  businessEvents: BizzEventModelT[];
  endUserEvents: EventModelT[];
};

export type CreateCommentFormT = {
  usrId: string;
  displayName: string;
  agendaId: string;
  Itemstart: string;
  commentId: string;
  comment: string;
  picture: string | null;
};

export type UpdateCommentFormT = {
  agendaId: string;
  commentId: string;
  comment: string;
};

export type DeleteCommentFormT = {
  agendaId: string;
  commentId: string;
};

export type ReportCommentFormT = {
  agendaId: string;
  commentId: string;
  usrId: string;
  reason: string;
};

export type CreateSubCommentFormT = Omit<CreateCommentFormT, "comment"> & {
  replyId: string;
  reply: string;
};

export type DeleteSubCommentFormT = {
  agendaId: string;
  replyId: string;
};

export type ReportSubCommentFormT = {
  agendaId: string;
  replyId: string;
  usrId: string;
  reason: string;
};

export type CreateRsvpFormT = {
  usrId: string;
  displayName: string;
  agendaId: string;
  Itemstart: string;
  rsvpStatus: string | "attending" | "notAttending";
  picture: string | null;
};

export type CreateImpressionFormT = {
  usrId: string;
  displayName: string;
  agendaId: string;
  Itemstart: string;
  picture: string | null;
};
