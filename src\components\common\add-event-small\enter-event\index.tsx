import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router";
import AchiveIcon from "@/assets/svg/send-archive-icon.svg";
import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { EventModelT, FileWithPreview } from "@/types";
import { Image } from "@/components/ui/image";
import PlaceholderImg from "@/assets/images/add-placeholder.jpg";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { SearchIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { DateTimePicker } from "../../date-time-picker";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import LocationImage from "@/assets/images/warsaw.png";
import InfoIcon from "@/assets/svg/Info-circle-icon.svg";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import {
  currentTimeZone,
  generateUUID,
  getPreviousHalfHour,
  getZoneFromTzCode,
} from "@/utils";
import { useCreateEvent } from "@/api/event";
import { useAuth } from "@/context/AuthContext";
import { addHours } from "date-fns";
import { FILE_TYPES, useCustomDropzone } from "@/hook/useCustomDropzone";

const ReminderEventTime = [
  { name: "5 Mins", value: "5-min" },
  { name: "10 Mins", value: "10-min" },
  { name: "15 Mins", value: "15-min" },
  { name: "30 Mins", value: "30-min" },
  { name: "1 Hour", value: "1-hour" },
  { name: "1 Day", value: "1-day" },
];

const RepeatEventTime = [
  { name: "Never", value: "never" },
  { name: "Daily", value: "daily" },
  { name: "Weekly", value: "weekly" },
  { name: "Monthly", value: "monthly" },
];

export default function AddEventEnterSmallView({
  onClose,
}: {
  onClose: () => void;
}) {
  const { userId } = useAuth();

  const { mutate, isPending } = useCreateEvent();
  const [eventImage, setEventImage] = useState<FileWithPreview[]>([]);
  const [attachmentPdf, setAttachmentPdf] = useState<FileWithPreview[]>([]);

  const { apiCalendars } = useCalendarsWithEvents(userId);

  const [eventName, setEventName] = useState("");
  const [chooseCalendar, setChooseCalendar] = useState<string | null>(null);
  const [reminderTime, setReminderTime] = useState<string | null>(null);
  const [repeatTime, setRepeatTime] = useState<string | null>(null);
  const [isAllDayEvent, setIsAllDayEvent] = useState(false);
  const [attachedLinks, setAttachedLinks] = useState<string[]>([""]);

  const [provideRSVP, setProvideRSVP] = useState(false);
  const [allowComments, setAllowComments] = useState(false);
  const [allowLikes, setAllowLikes] = useState(false);

  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");

  const [startDate, setStartDate] = useState<Date>(() => getPreviousHalfHour());
  const [endDate, setEndDate] = useState<Date>(() => {
    const start = getPreviousHalfHour();
    start.setHours(start.getHours() + 1);
    return start;
  });

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...attachedLinks];
    newLinks[index] = value;
    setAttachedLinks(newLinks);
  };

  const handleLinkRemove = (index: number) => {
    const updated = attachedLinks.filter((_, i) => i !== index);
    setAttachedLinks(updated);
  };

  const handleAddLink = () => {
    setAttachedLinks([...attachedLinks, ""]);
  };

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for event cover",
      "file-too-large": "Event image is too large",
      "too-many-files": "You can only upload one event image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setEventImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // No onFilesRejected - errors handled automatically
  });

  const { getRootProps: getRootPropsOfPdf, getInputProps: getInputPropsOfPdf } =
    useCustomDropzone({
      acceptedFileTypes: FILE_TYPES.DOCUMENTS,
      maxFiles: 1,
      maxSize: 1 * 1024 * 1024, // 1MB
      multiple: false,
      showToastOnError: true,
      customErrorMessages: {
        "file-invalid-type": "Please upload only PDF files",
        "file-too-large": "PDF file is too large",
        "too-many-files": "You can only upload one PDF file",
      },
      onFilesAccepted: (acceptedFiles) => {
        setAttachmentPdf(
          acceptedFiles.map((file) =>
            Object.assign(file, {
              preview: URL.createObjectURL(file),
            })
          )
        );
      },
      // No onFilesRejected - errors handled automatically
    });

  const handleCreateEvent = (e: FormEvent) => {
    e.preventDefault();

    if (!chooseCalendar || !apiCalendars) {
      toast.info("Calendar is required");
      return;
    }

    const calendar = apiCalendars?.find((cal) => cal._id === chooseCalendar);

    const FormData: EventModelT = {
      _id: generateUUID("agenda"),
      calId: chooseCalendar,
      calendarName: calendar?.calendarName || "",
      end: endDate.toISOString(),
      start: startDate.toISOString(),
      event: true,
      note: false,
      title: eventName,
      userType: "endUser",
      usrId: userId || "",
      zone: getZoneFromTzCode(currentTimeZone),
      allDayEvent: isAllDayEvent,
      deleted: false,
      location,
      description,
      frequency: repeatTime || "",
      days: repeatTime ? [repeatTime] : [],
      repeat: repeatTime != "Never",
      repeatEndDate: endDate.toISOString(),
      repeatEvery: repeatTime || "",
      picture: "",
      link: attachedLinks,
      color: calendar?.color || "#c0c3cb ",
      attach: null,
      textColor: "#454343",
      profileImage: eventImage[0] || null,
      notifyBefore: reminderTime || "",
      restrictRSVP: provideRSVP,
      allowComments: allowComments,
      allowLikes: allowLikes,
      restrict: [],
      createdAt: new Date().toISOString(),
      Comments: [],
      Impression: [],
      Reply: [],
      Rsvps: [],
    };

    console.log(FormData, "--- creat event formData--");
    mutate(FormData, {
      onSuccess() {
        toast.success("Event Created.");
        onClose();
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  return (
    <form
      onSubmit={handleCreateEvent}
      className="flex flex-1 gap-2 flex-col overflow-y-auto no-scrollbar ">
      <div className="px-2 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Link to={"/add-event"}>
            <img src={AchiveIcon} className="size-5" />
          </Link>
          <p className="text-black font-bold text-xl">New Event</p>
        </div>
        <Button
          type="button"
          variant={"ghost"}
          size={"icon"}
          className="!h-7 !w-7 rounded-full"
          onClick={onClose}>
          <X />
        </Button>
      </div>

      <div className="flex flex-col gap-1">
        <Label htmlFor="EventName" className="text-fb-neutral-400 pl-3">
          Event Name
        </Label>

        <Input
          required
          id="EventName"
          className="!rounded-12px border-fb-neutral-400"
          placeholder="Name"
          value={eventName}
          onChange={(e) => {
            setEventName(e.target.value);
          }}
        />
      </div>
      <div className="flex flex-col gap-1 border-b border-fb-neutral-200">
        <Label className="text-fb-neutral-400 pl-3 ">Choose Calendar</Label>
        <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-1">
          {apiCalendars?.map((cal, ind) => {
            return (
              <div
                onClick={() => {
                  setChooseCalendar(cal._id);
                }}
                key={ind}
                className={cn(
                  " border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                  chooseCalendar === cal._id
                    ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                    : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                )}
                style={{ backgroundColor: `${cal.color}50` }}>
                <div
                  className="w-3 h-3 min-w-3 rounded-full bg-fb-bPrime-500"
                  style={{ backgroundColor: cal.color }}
                />
                <p className="truncate text-xs font-semibold">
                  {cal.calendarName}
                </p>
              </div>
            );
          })}
        </div>
      </div>
      <div>
        <div className="flex items-center space-x-2">
          <Label htmlFor="all-day">All day</Label>
          <Switch
            id="all-day"
            size="md"
            checked={isAllDayEvent}
            onCheckedChange={(e) => {
              setIsAllDayEvent(e);
            }}
          />
        </div>
        <div className="w-full flex flex-col gap-0.5 items-center justify-center">
          <div className="flex gap-1 items-center">
            <DateTimePicker
              date={startDate}
              setDate={(e) => {
                setStartDate(e);
                setEndDate(addHours(e, 1));
              }}
              isSmallView
              showDay={false}
            />
            <div className="w-12 h-0.5 rounded-full bg-black/70" />
            <DateTimePicker
              date={endDate}
              setDate={(e) => {
                if (e.getTime() < startDate.getTime()) {
                  toast.info("End date cannot be before start date/time");
                  return;
                }
                setEndDate(e);
              }}
              isSmallView
              showDay={false}
            />
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-center justify-end">
        <div className=" ">
          <p className="text-fb-neutral-600 font-semibold text-sm">Reminder</p>
        </div>

        <Select
          value={reminderTime || undefined}
          onValueChange={setReminderTime}>
          <SelectTrigger className="w-fit bg-fb-neutral-100 border-fb-neutral-500 !rounded-12px h-8">
            <SelectValue placeholder="Slect Reminder" />
          </SelectTrigger>
          <SelectContent>
            {ReminderEventTime.map((remin, ind) => {
              return (
                <SelectItem value={remin.value} key={ind}>
                  {remin.name}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Accordion type="single" collapsible>
          <AccordionItem value="item-1">
            <AccordionTrigger className=" gap-2 text-sm text-black p-0">
              More Information
            </AccordionTrigger>
            <AccordionContent className="gap-2 flex flex-col">
              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Repeat this event :
                  </p>
                </div>
                <div className=" grid grid-cols-4 gap-1">
                  {RepeatEventTime.map((remind, ind) => {
                    return (
                      <div
                        className={cn(
                          "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                          repeatTime === remind.value
                            ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                            : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                        )}
                        key={ind}
                        onClick={() => {
                          setRepeatTime(remind.value);
                        }}>
                        {remind.name}
                      </div>
                    );
                  })}
                </div>
              </div>
              <Separator />
              <div className="flex flex-col w-full">
                {eventImage.length > 0 ? (
                  <div className="w-full h-32 rounded-12px overflow-hidden relative">
                    <Image
                      src={eventImage[0]?.preview}
                      fallbackSrc={PlaceholderImg}
                    />
                    <Button
                      type="button"
                      variant={"ghost"}
                      size={"icon"}
                      className="absolute top-2 right-2 h-6 w-6"
                      onClick={() => setEventImage([])}>
                      <X className="text-fb-warn-600" />
                    </Button>
                  </div>
                ) : (
                  <div
                    {...getRootProps({ className: "dropzone" })}
                    className="bg-fb-neutral-200 rounded-12px overflow-hidden h-32 flex justify-center items-center cursor-pointer relative">
                    <input {...getInputProps()} />
                    <Image src={PlaceholderImg} />
                    <div className="bg-fb-bPrime-placeholder/50 absolute top-0 left-0 w-full h-full flex justify-center items-center">
                      <p className="text-lg font-semibold">Add Image</p>
                    </div>
                  </div>
                )}
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className=" ">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Description :
                  </p>
                </div>
                <div className=" gap-1">
                  <Textarea
                    value={description}
                    onChange={(e) => {
                      setDescription(e.target.value);
                    }}
                    className="!rounded-12px border-fb-neutral-400 resize-none"
                    placeholder="Description"
                  />
                </div>
              </div>
              <Separator />

              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Location :
                  </p>
                </div>
                <div className="gap-2 flex flex-col">
                  <div className="flex items-center relative">
                    <Input
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="!rounded-12px border-fb-neutral-400 pr-8"
                      placeholder="Location"
                    />
                    <SearchIcon className="absolute right-3 size-4 text-fb-neutral-600" />
                  </div>
                  <div className="overflow-hidden rounded-12px h-28 w-full border border-fb-neutral-400">
                    <Image
                      src={LocationImage}
                      className="contrast-75 opacity-30"
                    />
                  </div>
                </div>
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Add Links
                  </p>
                </div>
                <div className=" gap-1 flex flex-col">
                  {attachedLinks.map((links, ind) => {
                    return (
                      <div className="relative flex items-center" key={ind}>
                        <Input
                          key={ind}
                          value={links}
                          type="url"
                          className="!rounded-12px border-fb-neutral-400 pr-8"
                          placeholder="Enter Link"
                          onChange={(e) => {
                            handleLinkChange(ind, e.target.value);
                          }}
                        />
                        <Button
                          type="button"
                          variant={"ghost"}
                          size={"icon"}
                          className="absolute right-1 !h-7 !w-7 rounded-full"
                          onClick={() => handleLinkRemove(ind)}>
                          <X />
                        </Button>
                      </div>
                    );
                  })}
                  <Button
                    type="button"
                    variant={"outline"}
                    className="!rounded-12px border-fb-neutral-400 text-fb-neutral-600 h-8"
                    onClick={handleAddLink}>
                    Add a New Link
                  </Button>
                </div>
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className=" ">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Attach Documents
                  </p>
                </div>
                <div className=" gap-2 flex flex-col">
                  {attachmentPdf.length > 0 ? (
                    <div>
                      <Button
                        size={"sm"}
                        variant={"ghost"}
                        type="button"
                        className="rounded-full h-7 text-fb-neutral-600"
                        onClick={() => {
                          setAttachmentPdf([]);
                        }}>
                        <X className="size-4" />
                        {attachmentPdf[0].name}
                      </Button>
                    </div>
                  ) : (
                    <div
                      {...getRootPropsOfPdf()}
                      className="w-28 min-w-36 lg:min-w-64 h-7 bg-fb-neutral-200 rounded-xl flex flex-col justify-center items-center gap-2 relative cursor-pointer">
                      <input {...getInputPropsOfPdf()} />

                      <p className="text-sm font-light text-fb-neutral-600">
                        Attach Pdf
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <Separator />
              <div className="flex flex-col gap-2">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    User Interactions
                  </p>
                </div>
                <div className=" gap-2 flex flex-col">
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="option-RSVP"
                      className="text-sm -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                      Provide RSVP
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch
                      id="option-RSVP"
                      size="md"
                      checked={provideRSVP}
                      onCheckedChange={(e) => {
                        setProvideRSVP(e);
                      }}
                    />
                  </div>
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="allow-comments"
                      className="text-sm  -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                      Allow Comments
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch
                      id="allow-comments"
                      size="md"
                      checked={allowComments}
                      onCheckedChange={(e) => {
                        setAllowComments(e);
                      }}
                    />
                  </div>
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="RSVP-comments"
                      className="text-sm -tracking-wider font-normal flex gap-0.5 cursor-pointer text-fb-neutral-600">
                      Allow Users see likes
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch
                      id="RSVP-comments"
                      size="md"
                      checked={allowLikes}
                      onCheckedChange={(e) => {
                        setAllowLikes(e);
                      }}
                    />
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      <Button
        disabled={isPending}
        type="submit"
        className="bg-fb-bPrime-600 !rounded-12px h-7 sticky bottom-1 mt-auto">
        Add Event
      </Button>
    </form>
  );
}
