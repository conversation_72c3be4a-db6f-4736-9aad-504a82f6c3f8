import { useState } from "react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import BgImage from "@/assets/images/bg-image.png";
import UserLogo from "@/assets/images/fragment-user-logo.png";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router";

export default function SignUpDetailsScreen() {
  const { userId } = useAuth();
  const [yourId, setYourId] = useState<string>("");
  const [fragment, setFragment] = useState<string>("");

  if (userId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-white flex w-full h-dvh sm:h-screen relative overflow-hidden">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />

      <div className="flex h-full px-4 py-6 w-1/2 z-10 ">
        <div className="bg-fb-neutral-100 flex w-full rounded-3xl px-6 sm:px-12 md:px-16 lg:px-24 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <div className="flex flex-col gap-12 w-full justify-center">
            <div className="border-b pb-1 border-black">
              <p className="text-3xl text-black">Build Your Profile</p>
            </div>
            <div className="flex flex-col gap-3 w-full   h-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="fragment"
                  className="text-black font-semibold text-2xl">
                  Your Name*
                </Label>
                <Input
                  id="fragment"
                  value={fragment || ""}
                  type="text"
                  placeholder="fragment"
                  onChange={(e) => setFragment(e.target.value)}
                  className=""
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="yourId"
                  className="text-black font-semibold text-2xl">
                  Choose your ID*
                </Label>
                <Input
                  id="yourId"
                  value={yourId || ""}
                  type="text"
                  placeholder="@fragBus"
                  onChange={(e) => setYourId(e.target.value)}
                  className=""
                />
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button className="text-xl font-semibold rounded-full text-black bg-fb-uPrime-500 hover:bg-fb-uPrime-500/80">
                  Sign Up
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-center items-center w-1/2 z-10">
        <div className="flex flex-col">
          <div className="mx-auto h-48">
            <img src={UserLogo} alt="" className="h-full object-cover" />
          </div>
          <p className="text-3xl font-bold text-black">your one step</p>
          <p className="text-[44px] leading-none font-bold text-black">
            Organiser
          </p>
        </div>
      </div>
    </div>
  );
}
